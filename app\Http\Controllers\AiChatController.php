<?php

namespace App\Http\Controllers;

use App\Models\ChatConversation;
use App\Services\GeminiChatService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class AiChatController extends Controller
{
    private GeminiChatService $geminiService;

    public function __construct(GeminiChatService $geminiService)
    {
        $this->geminiService = $geminiService;
    }

    /**
     * Send a message to the AI chat
     */
    public function sendMessage(Request $request): JsonResponse
    {
        try {
            // Rate limiting
            $key = 'chat:' . ($request->ip() ?? 'unknown');
            if (RateLimiter::tooManyAttempts($key, 10)) {
                return response()->json([
                    'success' => false,
                    'message' => 'تم تجاوز الحد المسموح من الرسائل. يرجى المحاولة بعد دقيقة.',
                    'error' => 'RATE_LIMIT'
                ], 429);
            }

            RateLimiter::hit($key, 60); // 10 requests per minute

            // Validation
            $validated = $request->validate([
                'message' => 'required|string|max:1000',
                'session_id' => 'required|string|max:100',
                'language' => 'sometimes|string|in:ar,en'
            ]);

            $message = trim($validated['message']);
            $sessionId = $validated['session_id'];
            $language = $validated['language'] ?? 'ar';
            $userId = Auth::id();

            // Get conversation history
            $history = ChatConversation::getBySession($sessionId, 5)->toArray();

            // Send to Gemini AI
            $aiResponse = $this->geminiService->sendMessage($message, $language, $history);

            if (!$aiResponse['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $aiResponse['message'],
                    'error' => $aiResponse['error'] ?? 'AI_ERROR'
                ], 500);
            }

            // Save conversation
            $conversation = ChatConversation::createConversation(
                $sessionId,
                $message,
                $aiResponse['message'],
                $language,
                $userId,
                [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'usage' => $aiResponse['usage'] ?? null
                ]
            );

            return response()->json([
                'success' => true,
                'message' => $aiResponse['message'],
                'conversation_id' => $conversation->id,
                'timestamp' => $conversation->created_at->toISOString()
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'البيانات المرسلة غير صحيحة.',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            \Log::error('AI Chat Controller Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
                'error' => 'INTERNAL_ERROR'
            ], 500);
        }
    }

    /**
     * Get conversation history
     */
    public function getHistory(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'session_id' => 'required|string|max:100',
                'limit' => 'sometimes|integer|min:1|max:50'
            ]);

            $sessionId = $validated['session_id'];
            $limit = $validated['limit'] ?? 20;

            $conversations = ChatConversation::getBySession($sessionId, $limit);

            return response()->json([
                'success' => true,
                'conversations' => $conversations->map(function ($conv) {
                    return [
                        'id' => $conv->id,
                        'user_message' => $conv->user_message,
                        'ai_response' => $conv->ai_response,
                        'language' => $conv->language,
                        'timestamp' => $conv->created_at->toISOString()
                    ];
                })
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'البيانات المرسلة غير صحيحة.',
                'errors' => $e->errors()
            ], 422);
        }
    }

    /**
     * Get quick response suggestions
     */
    public function getQuickResponses(Request $request): JsonResponse
    {
        $language = $request->get('language', 'ar');
        $responses = $this->geminiService->getQuickResponses($language);

        return response()->json([
            'success' => true,
            'quick_responses' => $responses
        ]);
    }

    /**
     * Generate a new session ID
     */
    public function generateSession(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'session_id' => 'chat_' . Str::random(32)
        ]);
    }
}
