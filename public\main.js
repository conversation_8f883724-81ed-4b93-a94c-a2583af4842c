// Language management
const defaultLanguage = 'en';
let currentLanguage = localStorage.getItem('language') ||
                     (navigator.language || navigator.userLanguage).substring(0, 2) ||
                     defaultLanguage;

// Initialize language on load
document.addEventListener('DOMContentLoaded', () => {
  setLanguage(currentLanguage);
});

// Language toggle functionality
const languageToggle = document.querySelector('.language-toggle');
languageToggle.addEventListener('click', () => {
  const newLanguage = currentLanguage === 'en' ? 'ar' : 'en';
  setLanguage(newLanguage);
});

function setLanguage(lang) {
  currentLanguage = lang;
  document.documentElement.lang = lang;
  document.body.dir = lang === 'ar' ? 'rtl' : 'ltr';

  // Update language toggle button
  const currentLangSpan = document.querySelector('.current-lang');
  const otherLangSpan = document.querySelector('.other-lang');
  if (lang === 'ar') {
    currentLangSpan.textContent = 'عربي';
    otherLangSpan.textContent = 'EN';
  } else {
    currentLangSpan.textContent = 'EN';
    otherLangSpan.textContent = 'عربي';
  }

  // Update all translatable elements
  document.querySelectorAll(`[data-${lang}]`).forEach(element => {
    element.textContent = element.getAttribute(`data-${lang}`);
  });

  // Store language preference
  localStorage.setItem('language', lang);
}

// Particle.js configuration - Enhanced for better visibility
const particlesConfig = {
  particles: {
    number: {
      value: 120, // زيادة عدد الجسيمات
      density: {
        enable: true,
        value_area: 600 // تقليل المساحة لزيادة الكثافة
      }
    },
    color: {
      value: ["#3AB0FF", "#1D8DF0", "#61C0FF", "#E67E22"] // ألوان متعددة متناسقة مع الموقع
    },
    shape: {
      type: "circle",
      stroke: {
        width: 1,
        color: "#3AB0FF"
      }
    },
    opacity: {
      value: 0.7, // زيادة الشفافية لوضوح أكبر
      random: true,
      anim: {
        enable: true,
        speed: 1,
        opacity_min: 0.3,
        sync: false
      }
    },
    size: {
      value: 4, // زيادة حجم الجسيمات
      random: true,
      anim: {
        enable: true,
        speed: 2,
        size_min: 1,
        sync: false
      }
    },
    line_linked: {
      enable: true,
      distance: 180, // زيادة مسافة الربط
      color: "#3AB0FF",
      opacity: 0.6, // زيادة وضوح الخطوط
      width: 2 // زيادة سمك الخطوط
    },
    move: {
      enable: true,
      speed: 3, // زيادة سرعة الحركة
      direction: "none",
      random: true,
      straight: false,
      out_mode: "bounce", // تغيير إلى bounce للحصول على تأثير أفضل
      bounce: true,
      attract: {
        enable: true,
        rotateX: 600,
        rotateY: 1200
      }
    }
  },
  interactivity: {
    detect_on: "canvas",
    events: {
      onhover: {
        enable: true,
        mode: ["grab", "bubble"] // إضافة تأثير bubble
      },
      onclick: {
        enable: true,
        mode: ["push", "repulse"] // إضافة تأثير repulse
      },
      resize: true
    },
    modes: {
      grab: {
        distance: 200, // زيادة مسافة التفاعل
        line_linked: {
          opacity: 0.8
        }
      },
      bubble: {
        distance: 250,
        size: 8,
        duration: 2,
        opacity: 0.8,
        speed: 3
      },
      repulse: {
        distance: 150,
        duration: 0.4
      },
      push: {
        particles_nb: 4
      }
    }
  },
  retina_detect: true
};

// Initialize particles
particlesJS('particles-js', particlesConfig);

// Enhanced particle interactions
document.addEventListener('DOMContentLoaded', function() {
  // Responsive particle configuration
  function updateParticleConfig() {
    const isMobile = window.innerWidth <= 768;
    const isTablet = window.innerWidth <= 1024 && window.innerWidth > 768;

    if (window.pJSDom && window.pJSDom[0] && window.pJSDom[0].pJS) {
      const pJS = window.pJSDom[0].pJS;

      if (isMobile) {
        pJS.particles.number.value = 60;
        pJS.particles.line_linked.distance = 120;
        pJS.particles.move.speed = 2;
      } else if (isTablet) {
        pJS.particles.number.value = 90;
        pJS.particles.line_linked.distance = 150;
        pJS.particles.move.speed = 2.5;
      } else {
        pJS.particles.number.value = 120;
        pJS.particles.line_linked.distance = 180;
        pJS.particles.move.speed = 3;
      }

      pJS.fn.particlesRefresh();
    }
  }

  // Add dynamic color changes based on time
  setInterval(() => {
    const hour = new Date().getHours();
    let colors;

    if (hour >= 6 && hour < 12) {
      // Morning colors - bright and energetic
      colors = ["#3AB0FF", "#61C0FF", "#87CEEB", "#E67E22"];
    } else if (hour >= 12 && hour < 18) {
      // Afternoon colors - warm and professional
      colors = ["#1D8DF0", "#3AB0FF", "#E67E22", "#F39C12"];
    } else {
      // Evening/Night colors - calm and sophisticated
      colors = ["#2C3E50", "#3AB0FF", "#8E44AD", "#E67E22"];
    }

    // Update particle colors dynamically
    if (window.pJSDom && window.pJSDom[0] && window.pJSDom[0].pJS) {
      window.pJSDom[0].pJS.particles.color.value = colors;
      window.pJSDom[0].pJS.particles.line_linked.color = colors[0];
    }
  }, 60000); // Update every minute

  // Add enhanced mouse interaction
  let mouseTrail = [];
  let lastMouseMove = 0;

  document.addEventListener('mousemove', (e) => {
    const now = Date.now();

    // Throttle mouse events for better performance
    if (now - lastMouseMove < 50) return;
    lastMouseMove = now;

    mouseTrail.push({
      x: e.clientX,
      y: e.clientY,
      time: now
    });

    // Keep only recent trail points
    mouseTrail = mouseTrail.filter(point => now - point.time < 1000);

    // Create temporary particles at mouse position (only on desktop)
    if (!window.matchMedia('(max-width: 768px)').matches &&
        window.pJSDom && window.pJSDom[0] && window.pJSDom[0].pJS &&
        Math.random() < 0.05) {

      const canvas = window.pJSDom[0].pJS.canvas.el;
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Add temporary particle effect
      if (window.pJSDom[0].pJS.particles.array.length < 150) {
        window.pJSDom[0].pJS.fn.modes.pushParticles(1, {
          pos_x: x,
          pos_y: y
        });
      }
    }
  });

  // Handle window resize
  window.addEventListener('resize', updateParticleConfig);

  // Initial configuration
  setTimeout(updateParticleConfig, 1000);

  // Add scroll-based particle effects
  let lastScrollY = 0;
  window.addEventListener('scroll', () => {
    const scrollY = window.scrollY;
    const scrollDelta = Math.abs(scrollY - lastScrollY);

    if (scrollDelta > 50 && window.pJSDom && window.pJSDom[0] && window.pJSDom[0].pJS) {
      // Increase particle activity during scroll
      const pJS = window.pJSDom[0].pJS;
      const originalSpeed = pJS.particles.move.speed;

      pJS.particles.move.speed = originalSpeed * 1.5;

      setTimeout(() => {
        if (pJS.particles.move) {
          pJS.particles.move.speed = originalSpeed;
        }
      }, 500);
    }

    lastScrollY = scrollY;
  });
});

// Loading screen
window.addEventListener('load', () => {
  const loadingScreen = document.querySelector('.loading-screen');
  if (loadingScreen) {
    setTimeout(() => {
      loadingScreen.style.opacity = '0';
      setTimeout(() => {
        loadingScreen.style.display = 'none';
      }, 500);
    }, 1500);
  }
});

// Mobile menu
const hamburger = document.querySelector('.hamburger');
const navLinks = document.querySelector('.nav-links');

hamburger.addEventListener('click', () => {
  navLinks.classList.toggle('active');
  hamburger.classList.toggle('active');
});

// Smooth scroll for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth'
      });
      // Close mobile menu if open
      navLinks.classList.remove('active');
      hamburger.classList.remove('active');
    }
  });
});

// Button hover effects
document.querySelectorAll('.cta-button').forEach(button => {
  button.addEventListener('mouseover', () => {
    button.style.transform = 'translateY(-2px)';
  });

  button.addEventListener('mouseout', () => {
    button.style.transform = 'translateY(0)';
  });
});