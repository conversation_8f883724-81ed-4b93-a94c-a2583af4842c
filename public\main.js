// Language management
const defaultLanguage = 'en';
let currentLanguage = localStorage.getItem('language') ||
                     (navigator.language || navigator.userLanguage).substring(0, 2) ||
                     defaultLanguage;

// Initialize language on load
document.addEventListener('DOMContentLoaded', () => {
  setLanguage(currentLanguage);
});

// Language toggle functionality
const languageToggle = document.querySelector('.language-toggle');
languageToggle.addEventListener('click', () => {
  const newLanguage = currentLanguage === 'en' ? 'ar' : 'en';
  setLanguage(newLanguage);
});

function setLanguage(lang) {
  currentLanguage = lang;
  document.documentElement.lang = lang;
  document.body.dir = lang === 'ar' ? 'rtl' : 'ltr';

  // Update language toggle button
  const currentLangSpan = document.querySelector('.current-lang');
  const otherLangSpan = document.querySelector('.other-lang');
  if (lang === 'ar') {
    currentLangSpan.textContent = 'عربي';
    otherLangSpan.textContent = 'EN';
  } else {
    currentLangSpan.textContent = 'EN';
    otherLangSpan.textContent = 'عربي';
  }

  // Update all translatable elements
  document.querySelectorAll(`[data-${lang}]`).forEach(element => {
    element.textContent = element.getAttribute(`data-${lang}`);
  });

  // Store language preference
  localStorage.setItem('language', lang);
}

// Particle.js configuration
const particlesConfig = {
  particles: {
    number: {
      value: 80,
      density: {
        enable: true,
        value_area: 800
      }
    },
    color: {
      value: "#4a90e2"
    },
    shape: {
      type: "circle"
    },
    opacity: {
      value: 0.5,
      random: false
    },
    size: {
      value: 3,
      random: true
    },
    line_linked: {
      enable: true,
      distance: 150,
      color: "#4a90e2",
      opacity: 0.4,
      width: 1
    },
    move: {
      enable: true,
      speed: 2,
      direction: "none",
      random: false,
      straight: false,
      out_mode: "out",
      bounce: false
    }
  },
  interactivity: {
    detect_on: "canvas",
    events: {
      onhover: {
        enable: true,
        mode: "grab"
      },
      onclick: {
        enable: true,
        mode: "push"
      },
      resize: true
    }
  },
  retina_detect: true
};

// Initialize particles
particlesJS('particles-js', particlesConfig);

// Loading screen
window.addEventListener('load', () => {
  const loadingScreen = document.querySelector('.loading-screen');
  if (loadingScreen) {
    setTimeout(() => {
      loadingScreen.style.opacity = '0';
      setTimeout(() => {
        loadingScreen.style.display = 'none';
      }, 500);
    }, 1500);
  }
});

// Mobile menu
const hamburger = document.querySelector('.hamburger');
const navLinks = document.querySelector('.nav-links');

hamburger.addEventListener('click', () => {
  navLinks.classList.toggle('active');
  hamburger.classList.toggle('active');
});

// Smooth scroll for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth'
      });
      // Close mobile menu if open
      navLinks.classList.remove('active');
      hamburger.classList.remove('active');
    }
  });
});

// Button hover effects
document.querySelectorAll('.cta-button').forEach(button => {
  button.addEventListener('mouseover', () => {
    button.style.transform = 'translateY(-2px)';
  });

  button.addEventListener('mouseout', () => {
    button.style.transform = 'translateY(0)';
  });
});