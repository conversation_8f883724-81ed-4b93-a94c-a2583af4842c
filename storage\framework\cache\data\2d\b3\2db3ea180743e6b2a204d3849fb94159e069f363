1748882366O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:25:{s:9:"site_name";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:1;s:3:"key";s:9:"site_name";s:5:"value";s:17:"منصة إنشر";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:19:"اسم الموقع";s:11:"description";s:72:"اسم الموقع الذي يظهر في العنوان والهيدر";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-29 06:11:26";}s:11:" * original";a:11:{s:2:"id";i:1;s:3:"key";s:9:"site_name";s:5:"value";s:17:"منصة إنشر";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:19:"اسم الموقع";s:11:"description";s:72:"اسم الموقع الذي يظهر في العنوان والهيدر";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-29 06:11:26";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:9:"site_logo";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:2;s:3:"key";s:9:"site_logo";s:5:"value";s:17:"images/enshir.ico";s:4:"type";s:5:"image";s:5:"group";s:10:"appearance";s:5:"label";s:21:"لوجو الموقع";s:11:"description";s:57:"لوجو الموقع الذي يظهر في الهيدر";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:2;s:3:"key";s:9:"site_logo";s:5:"value";s:17:"images/enshir.ico";s:4:"type";s:5:"image";s:5:"group";s:10:"appearance";s:5:"label";s:21:"لوجو الموقع";s:11:"description";s:57:"لوجو الموقع الذي يظهر في الهيدر";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:12:"site_favicon";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:3;s:3:"key";s:12:"site_favicon";s:5:"value";s:17:"images/enshir.ico";s:4:"type";s:5:"image";s:5:"group";s:10:"appearance";s:5:"label";s:35:"أيقونة الموقع (Favicon)";s:11:"description";s:65:"الأيقونة التي تظهر في تبويب المتصفح";s:10:"sort_order";i:3;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:3;s:3:"key";s:12:"site_favicon";s:5:"value";s:17:"images/enshir.ico";s:4:"type";s:5:"image";s:5:"group";s:10:"appearance";s:5:"label";s:35:"أيقونة الموقع (Favicon)";s:11:"description";s:65:"الأيقونة التي تظهر في تبويب المتصفح";s:10:"sort_order";i:3;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:16:"site_description";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:4;s:3:"key";s:16:"site_description";s:5:"value";s:111:"منصة إعلانات متنوعة تجمع بين البائعين والمشترين في مكان واحد";s:4:"type";s:8:"textarea";s:5:"group";s:7:"general";s:5:"label";s:19:"وصف الموقع";s:11:"description";s:68:"وصف مختصر للموقع يظهر في محركات البحث";s:10:"sort_order";i:4;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:4;s:3:"key";s:16:"site_description";s:5:"value";s:111:"منصة إعلانات متنوعة تجمع بين البائعين والمشترين في مكان واحد";s:4:"type";s:8:"textarea";s:5:"group";s:7:"general";s:5:"label";s:19:"وصف الموقع";s:11:"description";s:68:"وصف مختصر للموقع يظهر في محركات البحث";s:10:"sort_order";i:4;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:13:"site_keywords";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:5;s:3:"key";s:13:"site_keywords";s:5:"value";s:94:"إعلانات, تسوق, بيع, شراء, وظائف, عقارات, سيارات, خدمات";s:4:"type";s:8:"textarea";s:5:"group";s:7:"general";s:5:"label";s:33:"الكلمات المفتاحية";s:11:"description";s:72:"الكلمات المفتاحية للموقع لمحركات البحث";s:10:"sort_order";i:5;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:5;s:3:"key";s:13:"site_keywords";s:5:"value";s:94:"إعلانات, تسوق, بيع, شراء, وظائف, عقارات, سيارات, خدمات";s:4:"type";s:8:"textarea";s:5:"group";s:7:"general";s:5:"label";s:33:"الكلمات المفتاحية";s:11:"description";s:72:"الكلمات المفتاحية للموقع لمحركات البحث";s:10:"sort_order";i:5;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:13:"contact_email";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:6;s:3:"key";s:13:"contact_email";s:5:"value";s:15:"<EMAIL>";s:4:"type";s:5:"email";s:5:"group";s:7:"contact";s:5:"label";s:48:"البريد الإلكتروني للتواصل";s:11:"description";s:61:"البريد الإلكتروني الرئيسي للموقع";s:10:"sort_order";i:6;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:6;s:3:"key";s:13:"contact_email";s:5:"value";s:15:"<EMAIL>";s:4:"type";s:5:"email";s:5:"group";s:7:"contact";s:5:"label";s:48:"البريد الإلكتروني للتواصل";s:11:"description";s:61:"البريد الإلكتروني الرئيسي للموقع";s:10:"sort_order";i:6;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:13:"contact_phone";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:7;s:3:"key";s:13:"contact_phone";s:5:"value";s:13:"+966500000000";s:4:"type";s:4:"text";s:5:"group";s:7:"contact";s:5:"label";s:19:"رقم الهاتف";s:11:"description";s:34:"رقم الهاتف للتواصل";s:10:"sort_order";i:7;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:7;s:3:"key";s:13:"contact_phone";s:5:"value";s:13:"+966500000000";s:4:"type";s:4:"text";s:5:"group";s:7:"contact";s:5:"label";s:19:"رقم الهاتف";s:11:"description";s:34:"رقم الهاتف للتواصل";s:10:"sort_order";i:7;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:13:"primary_color";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:8;s:3:"key";s:13:"primary_color";s:5:"value";s:7:"#3ab0ff";s:4:"type";s:5:"color";s:5:"group";s:10:"appearance";s:5:"label";s:25:"اللون الأساسي";s:11:"description";s:38:"اللون الأساسي للموقع";s:10:"sort_order";i:8;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 13:19:55";}s:11:" * original";a:11:{s:2:"id";i:8;s:3:"key";s:13:"primary_color";s:5:"value";s:7:"#3ab0ff";s:4:"type";s:5:"color";s:5:"group";s:10:"appearance";s:5:"label";s:25:"اللون الأساسي";s:11:"description";s:38:"اللون الأساسي للموقع";s:10:"sort_order";i:8;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 13:19:55";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:15:"secondary_color";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:9;s:3:"key";s:15:"secondary_color";s:5:"value";s:7:"#f76809";s:4:"type";s:5:"color";s:5:"group";s:10:"appearance";s:5:"label";s:25:"اللون الثانوي";s:11:"description";s:38:"اللون الثانوي للموقع";s:10:"sort_order";i:9;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 13:32:42";}s:11:" * original";a:11:{s:2:"id";i:9;s:3:"key";s:15:"secondary_color";s:5:"value";s:7:"#f76809";s:4:"type";s:5:"color";s:5:"group";s:10:"appearance";s:5:"label";s:25:"اللون الثانوي";s:11:"description";s:38:"اللون الثانوي للموقع";s:10:"sort_order";i:9;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 13:32:42";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:16:"maintenance_mode";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:10;s:3:"key";s:16:"maintenance_mode";s:5:"value";s:1:"0";s:4:"type";s:7:"boolean";s:5:"group";s:7:"general";s:5:"label";s:21:"وضع الصيانة";s:11:"description";s:45:"تفعيل وضع الصيانة للموقع";s:10:"sort_order";i:10;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:10;s:3:"key";s:16:"maintenance_mode";s:5:"value";s:1:"0";s:4:"type";s:7:"boolean";s:5:"group";s:7:"general";s:5:"label";s:21:"وضع الصيانة";s:11:"description";s:45:"تفعيل وضع الصيانة للموقع";s:10:"sort_order";i:10;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:11:"site_slogan";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:11;s:3:"key";s:11:"site_slogan";s:5:"value";s:22:"سوقك لكل شيء";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:21:"شعار الموقع";s:11:"description";s:57:"الشعار الذي يظهر تحت اسم الموقع";s:10:"sort_order";i:11;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:11;s:3:"key";s:11:"site_slogan";s:5:"value";s:22:"سوقك لكل شيء";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:21:"شعار الموقع";s:11:"description";s:57:"الشعار الذي يظهر تحت اسم الموقع";s:10:"sort_order";i:11;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:11:"admin_email";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:12;s:3:"key";s:11:"admin_email";s:5:"value";s:16:"<EMAIL>";s:4:"type";s:5:"email";s:5:"group";s:7:"contact";s:5:"label";s:21:"بريد المدير";s:11:"description";s:46:"البريد الإلكتروني للمدير";s:10:"sort_order";i:12;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:12;s:3:"key";s:11:"admin_email";s:5:"value";s:16:"<EMAIL>";s:4:"type";s:5:"email";s:5:"group";s:7:"contact";s:5:"label";s:21:"بريد المدير";s:11:"description";s:46:"البريد الإلكتروني للمدير";s:10:"sort_order";i:12;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:12:"facebook_url";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:13;s:3:"key";s:12:"facebook_url";s:5:"value";s:27:"https://facebook.com/enshir";s:4:"type";s:4:"text";s:5:"group";s:6:"social";s:5:"label";s:21:"رابط فيسبوك";s:11:"description";s:34:"رابط صفحة الفيسبوك";s:10:"sort_order";i:13;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:13;s:3:"key";s:12:"facebook_url";s:5:"value";s:27:"https://facebook.com/enshir";s:4:"type";s:4:"text";s:5:"group";s:6:"social";s:5:"label";s:21:"رابط فيسبوك";s:11:"description";s:34:"رابط صفحة الفيسبوك";s:10:"sort_order";i:13;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:11:"twitter_url";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:14;s:3:"key";s:11:"twitter_url";s:5:"value";s:26:"https://twitter.com/enshir";s:4:"type";s:4:"text";s:5:"group";s:6:"social";s:5:"label";s:19:"رابط تويتر";s:11:"description";s:28:"رابط حساب تويتر";s:10:"sort_order";i:14;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:14;s:3:"key";s:11:"twitter_url";s:5:"value";s:26:"https://twitter.com/enshir";s:4:"type";s:4:"text";s:5:"group";s:6:"social";s:5:"label";s:19:"رابط تويتر";s:11:"description";s:28:"رابط حساب تويتر";s:10:"sort_order";i:14;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:13:"instagram_url";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:15;s:3:"key";s:13:"instagram_url";s:5:"value";s:28:"https://instagram.com/enshir";s:4:"type";s:4:"text";s:5:"group";s:6:"social";s:5:"label";s:25:"رابط إنستغرام";s:11:"description";s:34:"رابط حساب إنستغرام";s:10:"sort_order";i:15;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:15;s:3:"key";s:13:"instagram_url";s:5:"value";s:28:"https://instagram.com/enshir";s:4:"type";s:4:"text";s:5:"group";s:6:"social";s:5:"label";s:25:"رابط إنستغرام";s:11:"description";s:34:"رابط حساب إنستغرام";s:10:"sort_order";i:15;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:15:"whatsapp_number";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:16;s:3:"key";s:15:"whatsapp_number";s:5:"value";s:13:"+966500000000";s:4:"type";s:4:"text";s:5:"group";s:7:"contact";s:5:"label";s:19:"رقم واتساب";s:11:"description";s:47:"رقم واتساب للتواصل السريع";s:10:"sort_order";i:16;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:16;s:3:"key";s:15:"whatsapp_number";s:5:"value";s:13:"+966500000000";s:4:"type";s:4:"text";s:5:"group";s:7:"contact";s:5:"label";s:19:"رقم واتساب";s:11:"description";s:47:"رقم واتساب للتواصل السريع";s:10:"sort_order";i:16;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:12:"site_address";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:17;s:3:"key";s:12:"site_address";s:5:"value";s:61:"الرياض، المملكة العربية السعودية";s:4:"type";s:8:"textarea";s:5:"group";s:7:"contact";s:5:"label";s:23:"عنوان الموقع";s:11:"description";s:46:"العنوان الفيزيائي للشركة";s:10:"sort_order";i:17;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:17;s:3:"key";s:12:"site_address";s:5:"value";s:61:"الرياض، المملكة العربية السعودية";s:4:"type";s:8:"textarea";s:5:"group";s:7:"contact";s:5:"label";s:23:"عنوان الموقع";s:11:"description";s:46:"العنوان الفيزيائي للشركة";s:10:"sort_order";i:17;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:13:"working_hours";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:18;s:3:"key";s:13:"working_hours";s:5:"value";s:44:"الأحد - الخميس: 9:00 ص - 6:00 م";s:4:"type";s:8:"textarea";s:5:"group";s:7:"contact";s:5:"label";s:21:"ساعات العمل";s:11:"description";s:36:"ساعات العمل الرسمية";s:10:"sort_order";i:18;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:18;s:3:"key";s:13:"working_hours";s:5:"value";s:44:"الأحد - الخميس: 9:00 ص - 6:00 م";s:4:"type";s:8:"textarea";s:5:"group";s:7:"contact";s:5:"label";s:21:"ساعات العمل";s:11:"description";s:36:"ساعات العمل الرسمية";s:10:"sort_order";i:18;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:16:"google_analytics";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:19;s:3:"key";s:16:"google_analytics";s:5:"value";s:0:"";s:4:"type";s:8:"textarea";s:5:"group";s:3:"seo";s:5:"label";s:23:"كود Google Analytics";s:11:"description";s:32:"كود تتبع Google Analytics";s:10:"sort_order";i:19;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:19;s:3:"key";s:16:"google_analytics";s:5:"value";s:0:"";s:4:"type";s:8:"textarea";s:5:"group";s:3:"seo";s:5:"label";s:23:"كود Google Analytics";s:11:"description";s:32:"كود تتبع Google Analytics";s:10:"sort_order";i:19;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:11:"meta_author";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:20;s:3:"key";s:11:"meta_author";s:5:"value";s:17:"منصة إنشر";s:4:"type";s:4:"text";s:5:"group";s:3:"seo";s:5:"label";s:21:"مؤلف الموقع";s:11:"description";s:45:"اسم مؤلف الموقع للـ meta tags";s:10:"sort_order";i:20;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:20;s:3:"key";s:11:"meta_author";s:5:"value";s:17:"منصة إنشر";s:4:"type";s:4:"text";s:5:"group";s:3:"seo";s:5:"label";s:21:"مؤلف الموقع";s:11:"description";s:45:"اسم مؤلف الموقع للـ meta tags";s:10:"sort_order";i:20;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:14:"copyright_text";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:21;s:3:"key";s:14:"copyright_text";s:5:"value";s:62:"© 2025 منصة إنشر. جميع الحقوق محفوظة.";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:24:"نص حقوق الطبع";s:11:"description";s:38:"النص الذي يظهر في footer";s:10:"sort_order";i:21;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-29 06:15:52";}s:11:" * original";a:11:{s:2:"id";i:21;s:3:"key";s:14:"copyright_text";s:5:"value";s:62:"© 2025 منصة إنشر. جميع الحقوق محفوظة.";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:24:"نص حقوق الطبع";s:11:"description";s:38:"النص الذي يظهر في footer";s:10:"sort_order";i:21;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-29 06:15:52";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:15:"max_upload_size";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:22;s:3:"key";s:15:"max_upload_size";s:5:"value";s:1:"2";s:4:"type";s:6:"number";s:5:"group";s:7:"general";s:5:"label";s:46:"الحد الأقصى لحجم الملف (MB)";s:11:"description";s:85:"الحد الأقصى لحجم الملفات المرفوعة بالميجابايت";s:10:"sort_order";i:22;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-29 06:13:36";}s:11:" * original";a:11:{s:2:"id";i:22;s:3:"key";s:15:"max_upload_size";s:5:"value";s:1:"2";s:4:"type";s:6:"number";s:5:"group";s:7:"general";s:5:"label";s:46:"الحد الأقصى لحجم الملف (MB)";s:11:"description";s:85:"الحد الأقصى لحجم الملفات المرفوعة بالميجابايت";s:10:"sort_order";i:22;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-29 06:13:36";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:16:"default_language";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:23;s:3:"key";s:16:"default_language";s:5:"value";s:2:"ar";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:31:"اللغة الافتراضية";s:11:"description";s:44:"اللغة الافتراضية للموقع";s:10:"sort_order";i:23;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:23;s:3:"key";s:16:"default_language";s:5:"value";s:2:"ar";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:31:"اللغة الافتراضية";s:11:"description";s:44:"اللغة الافتراضية للموقع";s:10:"sort_order";i:23;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:8:"timezone";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:24;s:3:"key";s:8:"timezone";s:5:"value";s:11:"Asia/Riyadh";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:29:"المنطقة الزمنية";s:11:"description";s:42:"المنطقة الزمنية للموقع";s:10:"sort_order";i:24;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:24;s:3:"key";s:8:"timezone";s:5:"value";s:11:"Asia/Riyadh";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:29:"المنطقة الزمنية";s:11:"description";s:42:"المنطقة الزمنية للموقع";s:10:"sort_order";i:24;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:8:"currency";O:22:"App\Models\SiteSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:25;s:3:"key";s:8:"currency";s:5:"value";s:3:"SAR";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:33:"العملة الافتراضية";s:11:"description";s:49:"العملة المستخدمة في الموقع";s:10:"sort_order";i:25;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:11:" * original";a:11:{s:2:"id";i:25;s:3:"key";s:8:"currency";s:5:"value";s:3:"SAR";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:33:"العملة الافتراضية";s:11:"description";s:49:"العملة المستخدمة في الموقع";s:10:"sort_order";i:25;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-05-24 01:51:06";s:10:"updated_at";s:19:"2025-05-24 01:51:06";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}