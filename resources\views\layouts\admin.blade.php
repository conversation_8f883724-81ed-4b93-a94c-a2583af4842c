<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>@yield('title', 'لوحة تحكم المسؤولين | منصة انشر')</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

        <!-- Scripts -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

        <!-- Alpine.js -->
        <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

        <!-- Custom Styles -->
        @yield('styles')
        <style>
            /* تنسيقات Tailwind الأساسية */
            .bg-gray-100 { background-color: #f3f4f6; }
            .min-h-screen { min-height: 100vh; }
            .font-sans { font-family: 'Figtree', sans-serif; }
            .antialiased { -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }

            /* تنسيقات الأزرار */
            .btn-primary {
                background-color: #E67E22;
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 0.375rem;
                font-weight: 600;
                transition: all 0.3s ease;
                border-color: #E67E22;
            }

            .btn-primary:hover {
                background-color: #D35400;
                transform: translateY(-2px);
                box-shadow: 0 4px 6px rgba(230, 126, 34, 0.3);
                border-color: #D35400;
            }

            /* تنسيقات النماذج */
            .form-control {
                border-radius: 0.375rem;
                border: 1px solid #e5e7eb;
                padding: 0.5rem 0.75rem;
                transition: all 0.3s ease;
            }

            .form-control:focus {
                border-color: #E67E22;
                box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.25);
            }

            /* تنسيقات البطاقات */
            .card {
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                border: none;
                overflow: hidden;
            }

            .card-header {
                background-color: #f8fafc;
                border-bottom: 1px solid #e5e7eb;
                font-weight: 600;
            }
        </style>

        <style>
            body {
                font-family: 'Figtree', sans-serif;
                background-color: #f8f9fa;
            }

            /* تصميم الشريط الجانبي */
            .sidebar {
                width: 300px;
                position: fixed;
                right: 0;
                top: 0;
                height: 100vh;
                background: #343a40;
                padding: 20px;
                overflow-y: auto;
                z-index: 1000;
                box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
            }

            .sidebar-logo {
                text-align: center;
                padding: 20px 0;
                margin-bottom: 20px;
                border-bottom: 1px solid #eaeaea;
            }

            .sidebar-logo img {
                max-width: 150px;
                height: auto;
            }

            .menu-item {
                display: flex;
                align-items: center;
                padding: 16px 20px;
                margin: 10px 0;
                border-radius: 12px;
                background: rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.8);
                text-decoration: none;
                font-weight: 600;
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .menu-item:hover {
                background: #E67E22;
                color: white;
                transform: translateX(-8px);
                box-shadow: 0 8px 16px rgba(230, 126, 34, 0.2);
            }

            .menu-item.active {
                background: #E67E22;
                color: white;
            }

            .menu-item i {
                margin-left: 15px;
                font-size: 20px;
                width: 24px;
                text-align: center;
                transition: all 0.3s ease;
                color: #E67E22;
            }

            /* تغيير لون الأيقونات عند التحويم */
            .menu-item:hover i, .menu-item.active i {
                color: white !important;
            }

            .menu-item span {
                font-size: 15px;
                font-weight: 500;
            }

            /* تحسين المحتوى الرئيسي */
            main {
                margin-right: 320px;
                padding: 20px;
                flex-grow: 1;
            }

            /* التصميم المتجاوب */
            @media (max-width: 768px) {
                .sidebar {
                    width: 100%;
                    position: relative;
                    right: 0;
                    top: 0;
                    height: auto;
                    margin: 0 0 20px 0;
                    padding: 15px;
                    border-radius: 0;
                }

                main {
                    margin-right: 0;
                    padding: 15px;
                }

                .menu-item {
                    padding: 14px;
                    margin: 8px 0;
                    border-radius: 10px;
                    flex-direction: row;
                    justify-content: flex-start;
                }

                .menu-item i {
                    margin-left: 12px;
                    font-size: 18px;
                }

                .menu-item span {
                    font-size: 14px;
                }

                .menu-item:hover {
                    transform: translateX(-5px);
                }
            }

            /* تحسين شريط التمرير */
            .sidebar::-webkit-scrollbar {
                width: 6px;
            }

            .sidebar::-webkit-scrollbar-thumb {
                background: #E67E22;
                border-radius: 10px;
            }

            .sidebar::-webkit-scrollbar-track {
                background: #343a40;
                border-radius: 10px;
            }

            /* إضافة تأثيرات حركية */
            .menu-item::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 0;
                width: 0;
                height: 0;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                transform: translate(-50%, -50%);
                transition: width 0.6s ease, height 0.6s ease;
            }

            .menu-item:active::after {
                width: 400px;
                height: 400px;
            }

            /* تنسيقات إضافية للوحة التحكم */
            .stat-card {
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s;
                background-color: white;
                height: 100%;
            }

            .stat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            }

            .stat-icon {
                font-size: 2rem;
                margin-bottom: 15px;
                color: #E67E22;
            }

            .activity-icon {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .bg-primary-light {
                background-color: rgba(230, 126, 34, 0.15);
            }

            .bg-success-light {
                background-color: rgba(25, 135, 84, 0.15);
            }

            .bg-warning-light {
                background-color: rgba(255, 193, 7, 0.15);
            }

            .bg-danger-light {
                background-color: rgba(220, 53, 69, 0.15);
            }

            .bg-info-light {
                background-color: rgba(13, 202, 240, 0.15);
            }
        </style>
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="d-flex justify-content-center align-items-center mb-4">
                    <h5 class="text-white mb-0">منصة انشر</h5>
                </div>
                <hr class="bg-secondary">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="menu-item {{ request()->is('admin') || request()->is('admin/dashboard') ? 'active' : '' }}" href="{{ url('/admin') }}">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.users') ? 'active' : '' }}" href="{{ route('admin.users') }}">
                            <i class="fas fa-users"></i> المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.reports') || request()->routeIs('admin.reports.*') ? 'active' : '' }}" href="{{ route('admin.reports') }}">
                            <i class="fas fa-flag"></i> البلاغات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.jobs') ? 'active' : '' }}" href="{{ route('admin.jobs') }}">
                            <i class="fas fa-briefcase"></i> الوظائف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.ads') ? 'active' : '' }}" href="{{ route('admin.ads') }}">
                            <i class="fas fa-ad"></i> الإعلانات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.special-ads.*') ? 'active' : '' }}" href="{{ route('admin.special-ads.index') }}">
                            <i class="fas fa-star"></i> الإعلانات الخارجية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.job-seekers') ? 'active' : '' }}" href="{{ route('admin.job-seekers') }}">
                            <i class="fas fa-user-tie"></i> الباحثين عن عمل
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.bank-transfers.*') ? 'active' : '' }}" href="{{ route('admin.bank-transfers.index') }}">
                            <i class="fas fa-money-bill-wave"></i> التحويلات البنكية
                            @php
                                $pendingTransfersCount = \App\Models\PaymentTransaction::where('payment_method', 'bank_transfer')->where('status', 'PENDING')->count();
                            @endphp
                            @if($pendingTransfersCount > 0)
                                <span class="badge bg-warning rounded-pill ms-2">{{ $pendingTransfersCount }}</span>
                            @endif
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.points.add') ? 'active' : '' }}" href="{{ route('admin.points.add') }}">
                            <i class="fas fa-coins"></i> إضافة نقاط
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.user-roles.*') ? 'active' : '' }}" href="{{ route('admin.user-roles.index') }}">
                            <i class="fas fa-user-shield"></i> إدارة المسؤولين
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}" href="{{ route('admin.roles.index') }}">
                            <i class="fas fa-user-tag"></i> الأدوار والصلاحيات
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="menu-item {{ request()->routeIs('admin.site-settings.*') ? 'active' : '' }}" href="{{ route('admin.site-settings.index') }}">
                            <i class="fas fa-cogs"></i> إعدادات الموقع
                        </a>
                    </li>


                </ul>
                <hr class="bg-secondary">
                <div class="px-3 py-2">
                    <div class="d-flex align-items-center">
                        <img src="https://via.placeholder.com/40" class="rounded-circle" alt="Admin Profile">
                        <div class="ms-2">
                            <div class="small text-white">مرحباً، {{ Auth::user()->name }}</div>
                            <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="small text-light text-decoration-none">تسجيل الخروج</a>
                            <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                                @csrf
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <main class="main-content">
                <div class="container-fluid">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">@yield('page-title', 'لوحة تحكم المسؤولين')</h4>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-0">
                                        <li class="breadcrumb-item"><a href="{{ url('/admin') }}">الرئيسية</a></li>
                                        @yield('breadcrumb')
                                    </ol>
                                </nav>
                            </div>
                        </div>
                    </div>

                    @yield('content')
                </div>
            </main>
        </div>

        <script>
            // إضافة سكريبت للتأكد من تحميل Bootstrap بشكل صحيح
            document.addEventListener('DOMContentLoaded', function() {
                // التأكد من تحميل Bootstrap Modal
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl)
                });
            });
        </script>
    </body>
</html>
