
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعلانات | منصة انشر</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --primary-color: #3AB0FF;
            --primary-light: #61C0FF;
            --primary-dark: #1D8DF0;
            --secondary-color: #F2F2F2;
            --accent-color: #3AB0FF;
            --light-bg: #F8F9FA;
            --card-bg: #FFFFFF;
            --text-dark: #333333;
            --text-light: #6b7280;
            --text-white: #FFFFFF;
            --border-color: #E5E7EB;
            --success-color: #6BCB77;
            --warning-color: #FFB347;
            --error-color: #FF6B6B;
            --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        body {
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        /* تنسيق زر التحديث */
        .refresh-button-container {
            z-index: 1001;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: linear-gradient(to right, #1e293b, #0f172a);
            color: var(--text-white);
            padding: 1rem 0;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
            overflow: hidden;
        }

        header::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(to right, var(--primary-light), #E67E22);
        }

        /* حدود علوية للهيدر */
        header::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(to left, var(--primary-light), #E67E22);
            z-index: 1;
        }

        /* نمط خلفية الهيدر */
        .header-pattern {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 100%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
            z-index: 0;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .logo {
            font-size: 1.75rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .logo i {
            font-size: 2rem;
            color: var(--accent-color);
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 1rem;
        }

        nav a {
            color: var(--text-white);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover, nav a.active {
            background-color: rgba(230, 126, 34, 0.2); /* لون برتقالي غامق بشفافية */
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        nav a i {
            font-size: 1.1rem;
            color: #E67E22; /* لون برتقالي غامق */
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.6rem 1.25rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-login {
            background-color: transparent;
            color: var(--text-white);
            border: 2px solid var(--text-white);
        }

        .btn-login:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .btn-signup {
            background-color: var(--accent-color);
            color: var(--text-dark);
            font-weight: 700;
        }

        .btn-signup:hover {
            background-color: #f3a533;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* Search Bar - منصة انشر */
        .search-container {
            display: flex;
            margin: 15px 0;
            gap: 10px;
        }

        .add-ad-btn {
            background-color: var(--primary-color);
            color: var(--text-white);
            padding: 8px 15px;
            border-radius: 6px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: var(--transition);
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .add-ad-btn:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .search-box {
            display: flex;
            flex: 1;
        }

        .search-input {
            flex: 1;
            padding: 8px 15px;
            border: 1px solid #ddd;
            border-right: none;
            border-radius: 0 6px 6px 0;
            font-size: 14px;
            text-align: right;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(58, 176, 255, 0.2);
        }

        .search-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0 15px;
            border-radius: 6px 0 0 6px;
            cursor: pointer;
            transition: var(--transition);
        }

        .search-btn:hover {
            background-color: var(--primary-dark);
        }

        /* Categories - منصة انشر */
        .categories-container {
            position: relative;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .categories-scroll {
            display: flex;
            overflow-x: auto;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            padding: 10px 5px;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            gap: 15px;
            position: relative;
        }

        .categories-scroll::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .categories {
            display: flex;
            gap: 15px;
            padding: 0 5px;
        }

        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #333;
            padding: 12px;
            border-radius: 12px;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #eee;
            min-width: 80px;
            position: relative;
            overflow: hidden;
        }

        .category-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 102, 204, 0.1), rgba(0, 102, 204, 0));
            z-index: 0;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .category-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
        }

        .category-item:hover::before {
            opacity: 1;
        }

        .category-item.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 5px 15px rgba(58, 176, 255, 0.3);
        }

        .category-item.active .category-icon {
            color: white;
        }

        .category-icon {
            color: #E67E22; /* لون برتقالي غامق */
            font-size: 28px;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
            transition: var(--transition);
        }

        .category-name {
            font-size: 13px;
            text-align: center;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }

        /* أنماط التصنيفات الفرعية */
        .subcategories-container {
            margin: 0 0 15px 0;
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 10px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05) inset;
        }

        .subcategories-scroll {
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            padding: 5px;
        }

        .subcategories-scroll::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .subcategories {
            display: flex;
            gap: 10px;
            padding: 0 5px;
        }

        .subcategory-item {
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: #555;
            padding: 8px 15px;
            border-radius: 50px;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border: 1px solid #eee;
            white-space: nowrap;
            font-size: 13px;
            font-weight: 500;
        }

        .subcategory-item:hover {
            background-color: #f1f5f9;
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .subcategory-item.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 4px 10px rgba(58, 176, 255, 0.3);
        }

        .scroll-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background-color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            z-index: 20;
            border: 1px solid #eee;
            transition: all 0.3s ease;
            color: var(--primary-color);
        }

        .scroll-arrow:hover {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 5px 15px rgba(58, 176, 255, 0.3);
            transform: translateY(-50%) scale(1.1);
        }

        .scroll-arrow:active {
            transform: translateY(-50%) scale(0.95);
            transition: transform 0.1s;
        }

        .scroll-left {
            left: 5px;
        }

        .scroll-right {
            right: 5px;
        }

        /* تحسين ظهور أزرار التمرير في وضع سطح المكتب */
        @media (min-width: 769px) {
            .scroll-arrow {
                width: 45px;
                height: 45px;
                font-size: 1.1rem;
                opacity: 0.9;
            }

            .scroll-left {
                left: 10px;
            }

            .scroll-right {
                right: 10px;
            }

            .categories-container:hover .scroll-arrow {
                opacity: 1;
            }
        }

        /* Filter Bar - منصة انشر */
        .filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #eee;
            padding: 8px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }

        .filter-options {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .filter-btn {
            color: #333333;
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            background: none;
            border: none;
            font-size: 14px;
            transition: var(--transition);
        }

        .filter-btn i {
            color: #E67E22; /* لون برتقالي غامق للأيقونة */
        }

        .filter-btn:hover {
            color: var(--primary-dark);
        }

        .sort-dropdown {
            position: relative;
        }

        .sort-select {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 30px 6px 10px;
            font-size: 14px;
            color: #333;
            cursor: pointer;
            background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: left 8px center;
            background-size: 12px;
            min-width: 140px;
        }

        .sort-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(58, 176, 255, 0.2);
        }

        .sort-form {
            margin: 0;
        }

        .location-filters {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }

        .location-icon {
            color: var(--primary-color);
        }

        /* Search Section */
        .search-section {
            padding: 3rem 0;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            position: relative;
            overflow: hidden;
        }

        /* Animation pattern for background */
        .search-section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 100%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            z-index: 1;
        }

        .search-section .container {
            position: relative;
            z-index: 2;
        }

        .search-title {
            text-align: center;
            margin-bottom: 2.5rem;
            color: var(--text-white);
        }

        .search-title h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 800;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }

        .search-title p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            margin-bottom: 1.5rem;
        }

        .admin-action {
            margin-bottom: 2rem;
        }

        .admin-action .btn-signup {
            font-size: 1.1rem;
            padding: 0.75rem 1.5rem;
            background: var(--secondary-color);
            color: white;
        }

        .admin-action .btn-signup:hover {
            background: #047857;
        }

        .search-form {
            display: flex;
            gap: 0.5rem;
            max-width: 900px;
            margin: 0 auto;
            background-color: var(--card-bg);
            padding: 1.5rem;
            border-radius: 16px;
            box-shadow: var(--shadow);
            position: relative;
        }

        .form-group {
            flex: 1;
            position: relative;
        }

        .form-group i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-light);
            font-size: 1.2rem;
        }

        .search-form input,
        .search-form select {
            width: 100%;
            padding: 0.9rem 2.5rem 0.9rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s;
            background-color: #f9fafb;
        }

        .search-form input:focus,
        .search-form select:focus {
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
            outline: none;
            background-color: white;
        }

        .search-form button {
            background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
            color: var(--text-white);
            padding: 0.9rem 2rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 600;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .search-form button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.15);
        }

        /* Jobs Section */
        .jobs-section {
            padding: 3rem 0;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .section-header h2 {
            font-size: 1.8rem;
            color: var(--primary-dark);
            font-weight: 700;
            position: relative;
            padding-right: 1rem;
        }

        .section-header h2::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            width: 4px;
            background-color: var(--accent-color);
            border-radius: 2px;
        }

        .job-count {
            background-color: #e0f2fe;
            color: var(--primary-dark);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
            display: inline-block;
        }

        .sort-options {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: white;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            border: 1px solid #e5e7eb;
        }

        .sort-options span {
            color: var(--text-light);
            font-weight: 600;
        }

        .sort-options select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--card-bg);
            color: var(--primary-dark);
            font-weight: 500;
            cursor: pointer;
        }

        .job-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }

        .job-card {
            background-color: var(--card-bg);
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            padding: 1.75rem;
            transition: var(--transition);
            border: 1px solid #f1f5f9;
            position: relative;
            overflow: hidden;
            opacity: 0;
            animation: fadeIn 0.5s ease forwards;
        }

        .job-card:hover {
            transform: translateY(-7px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .job-card::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
            border-radius: 0 16px 16px 0;
        }

        .job-badge {
            position: absolute;
            top: 1.5rem;
            left: 1.5rem;
            background: linear-gradient(to right, #fde68a, #fbbf24);
            color: #92400e;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
            box-shadow: 0 2px 5px rgba(251, 191, 36, 0.3);
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .job-badge i {
            font-size: 0.7rem;
        }

        .job-card h3 {
            color: var(--primary-dark);
            margin-bottom: 0.75rem;
            font-size: 1.35rem;
            line-height: 1.3;
        }

        .job-company {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.25rem;
        }

        .company-logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(to bottom right, var(--primary-light), var(--primary-dark));
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-white);
            font-weight: 700;
            font-size: 1.5rem;
            box-shadow: 0 3px 8px rgba(30, 64, 175, 0.2);
        }

        .job-details {
            margin-bottom: 1.25rem;
            background-color: #f8fafc;
            border-radius: 10px;
            padding: 1rem;
        }

        .job-detail {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
            color: var(--text-dark);
        }

        .job-detail i {
            color: var(--primary-light);
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .job-description {
            margin-top: 1.25rem;
            margin-bottom: 1.5rem;
            color: var(--text-light);
            line-height: 1.6;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .job-description::after {
            content: "...";
            position: absolute;
            bottom: 0;
            right: 0;
            background-color: var(--card-bg);
            padding-left: 0.3rem;
        }

        .job-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.5rem;
            gap: 0.75rem;
        }

        .btn-apply {
            background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }

        .btn-apply:hover {
            background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(30, 64, 175, 0.3);
        }

        .btn-save {
            background-color: #f1f5f9;
            color: var(--text-light);
            border: 1px solid #e2e8f0;
            padding: 0.6rem;
            border-radius: 8px;
        }

        .btn-save:hover {
            background-color: #e2e8f0;
            color: var(--primary-dark);
        }

        .contact-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-top: 1.25rem;
        }

        .btn-whatsapp {
            background-color: #25D366;
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }

        .btn-whatsapp:hover {
            background-color: #22c55e;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(34, 197, 94, 0.3);
        }

        .btn-email {
            background-color: #EA4335;
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }

        .btn-email:hover {
            background-color: #dc2626;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(220, 38, 38, 0.3);
        }

        .btn-phone {
            background-color: #0284c7;
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }

        .btn-phone:hover {
            background-color: #0369a1;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(3, 105, 161, 0.3);
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 3rem;
            background-color: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        .pagination-inner {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-nav {
            padding: 0.7rem 1.5rem;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color));
            color: white;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-nav:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
        }

        .page-nav.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .page-number {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            color: var(--primary-dark);
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            border: 1px solid #e5e7eb;
        }

        .page-number:hover {
            background-color: #f1f5f9;
            border-color: #cbd5e1;
        }

        .page-number.active {
            background: linear-gradient(to right, var(--primary-light), var(--primary-color));
            color: white;
            border: none;
            box-shadow: 0 3px 10px rgba(59, 130, 246, 0.3);
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background-color: var(--card-bg);
            border-radius: 16px;
            box-shadow: var(--shadow);
        }

        .empty-state i {
            font-size: 4rem;
            color: #cbd5e1;
            margin-bottom: 1.5rem;
            display: block;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: var(--text-dark);
            font-size: 1.75rem;
        }

        .empty-state p {
            color: var(--text-light);
            margin-bottom: 2rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
            font-size: 1.1rem;
        }

        .empty-state .btn {
            font-size: 1.1rem;
            padding: 0.75rem 2rem;
        }

        /* Footer */
        footer {
            background: linear-gradient(to right, #1e293b, #0f172a);
            color: var(--text-white);
            padding: 4rem 0 2rem;
            margin-top: 4rem;
            position: relative;
            overflow: hidden;
        }

        footer::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(to right, var(--primary-light), var(--accent-color));
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 3rem;
        }

        .footer-column h3 {
            margin-bottom: 1.5rem;
            color: var(--text-white);
            font-size: 1.3rem;
            position: relative;
            padding-bottom: 0.75rem;
        }

        .footer-column h3::after {
            content: "";
            position: absolute;
            bottom: 0;
            right: 0;
            width: 50px;
            height: 3px;
            background-color: var(--accent-color);
            border-radius: 2px;
        }

        .footer-links {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links a {
            color: #cbd5e1;
            text-decoration: none;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .footer-links a::before {
            content: "\f054";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            font-size: 0.7rem;
            color: #E67E22; /* لون برتقالي غامق */
        }

        .footer-links a:hover {
            color: var(--text-white);
            transform: translateX(-5px);
        }

        .footer-bottom {
            margin-top: 3rem;
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #334155;
            color: #94a3b8;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animated {
            animation: fadeIn 0.5s ease forwards;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        @keyframes slideInRight {
            from {
                transform: translateX(30px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-30px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -1000px 0;
            }
            100% {
                background-position: 1000px 0;
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.5s ease forwards;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        .animate-slideInRight {
            animation: slideInRight 0.5s ease forwards;
        }

        .animate-slideInLeft {
            animation: slideInLeft 0.5s ease forwards;
        }

        .animate-shimmer {
            background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
            background-size: 1000px 100%;
            animation: shimmer 2s infinite linear;
        }

        .animate-delay-1 {
            animation-delay: 0.1s;
        }

        .animate-delay-2 {
            animation-delay: 0.2s;
        }

        .animate-delay-3 {
            animation-delay: 0.3s;
        }

        .animate-delay-4 {
            animation-delay: 0.4s;
        }

        .animate-delay-5 {
            animation-delay: 0.5s;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1.5rem;
                padding: 1rem 0;
            }

            nav ul {
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
            }

            nav a {
                padding: 0.5rem;
                font-size: 0.9rem;
            }

            .search-container {
                flex-direction: column;
                gap: 10px;
            }

            .add-ad-btn {
                width: 100%;
                justify-content: center;
                padding: 12px;
                font-size: 16px;
                border-radius: 10px;
            }

            .search-box {
                width: 100%;
            }

            .search-input, .search-btn {
                padding: 12px 15px;
                font-size: 16px;
            }

            .filter-bar {
                flex-direction: column;
                gap: 15px;
                padding: 15px;
            }

            .filter-options {
                width: 100%;
                justify-content: space-between;
            }

            .sort-select {
                width: 100%;
                padding: 10px;
                font-size: 16px;
            }

            .location-filters {
                width: 100%;
                justify-content: space-around;
            }

            .ad-item {
                border-radius: 15px;
                margin-bottom: 15px;
            }

            .ad-image {
                height: 180px !important;
            }

            .ad-details {
                padding: 15px;
            }

            .ad-details h3 {
                font-size: 18px;
                margin-bottom: 8px;
            }

            .categories-container {
                margin: 20px 0;
            }

            .categories-scroll {
                padding: 15px 5px;
            }

            .category-item {
                min-width: 100px;
                padding: 15px;
            }

            .category-icon {
                font-size: 32px;
            }

            .category-name {
                font-size: 14px;
            }

            .scroll-arrow {
                width: 36px;
                height: 36px;
            }

            .search-title h1 {
                font-size: 1.8rem;
            }

            .search-form {
                flex-direction: column;
                padding: 1rem;
            }

            .form-group {
                margin-bottom: 0.75rem;
            }

            .job-cards {
                grid-template-columns: 1fr;
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .job-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .contact-buttons {
                flex-direction: column;
            }

            .pagination-inner {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        @media (max-width: 576px) {
            .logo {
                font-size: 1.4rem;
            }

            .auth-buttons {
                flex-direction: column;
                gap: 0.5rem;
                width: 100%;
            }

            .auth-buttons .btn {
                width: 100%;
            }

            .search-title h1 {
                font-size: 1.5rem;
            }

            .search-title p {
                font-size: 1rem;
            }

            .search-form button {
                width: 100%;
                justify-content: center;
            }

            .pagination {
                padding: 1rem;
            }

            .ad-image {
                height: 160px !important;
            }
        }
    </style>
</head>
<body>
    <!-- زر تحديث الموقع العائم -->
    @include('components.refresh-button', [
        'position' => 'fixed',
        'size' => 'normal',
        'style' => 'floating',
        'showText' => false
    ])

    <!-- زر الإعدادات العائم -->
    @include('components.settings-button', [
        'position' => 'fixed',
        'size' => 'normal',
        'style' => 'floating',
        'showText' => false
    ])

    <!-- Header -->
    <header>
        <div class="container">
            <div class="header-content">
                <nav>
                    <ul>
                        <li><a href="{{ url('/') }}"><i class="fas fa-home"></i> الرئيسية</a></li>
                        <li><a href="{{ route('job_seekers.index') }}"><i class="fas fa-building"></i> الباحثين عن عمل </a></li>
                        <li><a href="{{ route('jobs.index') }}"><i class="fas fa-search"></i> الوظائف</a></li>
                       <li><a href="{{ route('ads.index') }}"><i class="fas fa-bullhorn"></i> الإعلانات</a></li>
                        <li><a href="{{ url('/dashboard') }}" class="flex items-center space-x-2 text-blue-500 hover:text-blue-700 border border-transparent hover:border-blue-500 px-4 py-2 rounded-lg bg-transparent hover:bg-blue-100 transition-all duration-300">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

@php
    $cities = [
        'السعودية' => [
            'الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام', 'الخبر', 'الظهران',
            'الطائف', 'بريدة', 'تبوك', 'خميس مشيط', 'حائل', 'الجبيل', 'ينبع', 'الأحساء',
            'نجران', 'جازان', 'عرعر', 'سكاكا', 'أبها', 'القطيف', 'الباحة', 'القريات',
            'رفحاء', 'ضبا', 'املج', 'الوجه', 'حقل', 'طريف', 'الدوادمي', 'المجمعة',
            'الزلفي', 'شقراء', 'عفيف', 'الرس', 'عنيزة', 'الكرك', 'القنفذة', 'الليث'
        ],
        'مصر' => [
            'القاهرة', 'الإسكندرية', 'الجيزة', 'شرم الشيخ', 'الغردقة', 'أسوان', 'الأقصر',
            'بورسعيد', 'السويس', 'الإسماعيلية', 'طنطا', 'المنصورة', 'الزقازيق', 'أسيوط',
            'المنيا', 'بني سويف', 'الفيوم', 'قنا', 'سوهاج', 'دمياط', 'كفر الشيخ',
            'مرسى مطروح', 'العريش', 'مدينة نصر', 'مصر الجديدة', 'المعادي', 'الزمالك'
        ],
        'الإمارات' => [
            'دبي', 'أبو ظبي', 'الشارقة', 'عجمان', 'أم القيوين', 'رأس الخيمة', 'الفجيرة',
            'العين', 'الذيد', 'خورفكان', 'كلباء', 'دبا الفجيرة', 'جبل علي', 'مدينة زايد',
            'ليوا', 'غياثي', 'مرفأ', 'الرويس', 'جزيرة دلما', 'السلع', 'مليحة'
        ],
        'قطر' => [
            'الدوحة', 'الوكرة', 'الخور', 'الريان', 'أم صلال', 'الشمال', 'الشحانية',
            'دخان', 'مسيعيد', 'الزبارة', 'الغويرية', 'فويرط', 'الجميلية', 'الكعبان'
        ],
        'الكويت' => [
            'مدينة الكويت', 'حولي', 'الأحمدي', 'الجهراء', 'الفروانية', 'مبارك الكبير',
            'السالمية', 'الفحيحيل', 'المنقف', 'الفنطاس', 'الرقة', 'الصباحية',
            'الوفرة', 'الخيران', 'الزور', 'كاظمة', 'أم الهيمان', 'الشعيبة'
        ],
        'البحرين' => [
            'المنامة', 'المحرق', 'الرفاع', 'مدينة حمد', 'مدينة عيسى', 'سترة', 'الزلاق',
            'البديع', 'جدحفص', 'عالي', 'الدراز', 'كرباباد', 'توبلي', 'سنابس',
            'الحد', 'عسكر', 'الدير', 'الحورة', 'جو', 'الماحوز'
        ],
        'عمان' => [
            'مسقط', 'صلالة', 'صحار', 'نزوى', 'صور', 'الرستاق', 'عبري', 'البريمي',
            'خصب', 'شناص', 'بهلاء', 'إزكي', 'الحمراء', 'منح', 'ضنك', 'ينقل',
            'المضيبي', 'إبراء', 'الكامل والوافي', 'جعلان بني بوحسن', 'مرباط', 'ثمريت'
        ],
        'الأردن' => [
            'عمان', 'إربد', 'الزرقاء', 'العقبة', 'السلط', 'مادبا', 'الكرك', 'معان',
            'الطفيلة', 'جرش', 'عجلون', 'المفرق', 'الرمثا', 'الرصيفة', 'وادي السير',
            'الجبيهة', 'أبو نصير', 'الجويدة', 'ماركا', 'القويسمة', 'سحاب'
        ],
        'لبنان' => [
            'بيروت', 'طرابلس', 'صيدا', 'صور', 'زحلة', 'جونيه', 'بعلبك', 'النبطية',
            'جبيل', 'البترون', 'الميناء', 'حلبا', 'راشيا', 'مرجعيون', 'بنت جبيل',
            'الهرمل', 'عكار العتيقة', 'القبيات', 'عاليه', 'بحمدون', 'برمانا'
        ],
        'المغرب' => [
            'الرباط', 'الدار البيضاء', 'مراكش', 'فاس', 'طنجة', 'أكادير', 'مكناس',
            'وجدة', 'القنيطرة', 'تطوان', 'سلا', 'المحمدية', 'تمارة', 'الجديدة',
            'بني ملال', 'الناظور', 'خريبكة', 'سطات', 'برشيد', 'إنزكان', 'الحسيمة'
        ],
        'تونس' => [
            'تونس', 'صفاقس', 'سوسة', 'القيروان', 'بنزرت', 'قابس', 'أريانة', 'قفصة',
            'المنستير', 'المهدية', 'تطاوين', 'قبلي', 'توزر', 'الكاف', 'سيدي بوزيد',
            'جندوبة', 'مدنين', 'نابل', 'زغوان', 'باجة', 'منوبة'
        ],
        'الجزائر' => [
            'الجزائر', 'وهران', 'قسنطينة', 'عنابة', 'باتنة', 'سطيف', 'سيدي بلعباس',
            'بسكرة', 'تلمسان', 'بجاية', 'تيزي وزو', 'الشلف', 'ورقلة', 'سكيكدة',
            'جيجل', 'تبسة', 'المسيلة', 'الوادي', 'خنشلة', 'سوق أهراس', 'معسكر'
        ],
        'ليبيا' => [
            'طرابلس', 'بنغازي', 'مصراتة', 'الزاوية', 'البيضاء', 'طبرق', 'الخمس',
            'زليتن', 'أجدابيا', 'سبها', 'درنة', 'صبراتة', 'زوارة', 'غريان',
            'يفرن', 'نالوت', 'غدامس', 'مرزق', 'الكفرة', 'أوباري', 'هون'
        ],
        'العراق' => [
            'بغداد', 'البصرة', 'الموصل', 'أربيل', 'النجف', 'كربلاء', 'السليمانية',
            'الحلة', 'الرمادي', 'الناصرية', 'العمارة', 'الديوانية', 'الكوت',
            'سامراء', 'تكريت', 'الفلوجة', 'بعقوبة', 'كركوك', 'دهوك', 'زاخو'
        ],
        'سوريا' => [
            'دمشق', 'حلب', 'حمص', 'حماة', 'اللاذقية', 'دير الزور', 'الرقة', 'درعا',
            'السويداء', 'طرطوس', 'إدلب', 'الحسكة', 'القامشلي', 'جبلة', 'بانياس',
            'صافيتا', 'معلولا', 'النبك', 'القطيفة', 'دوما', 'جرمانا'
        ],
        'فلسطين' => [
            'القدس', 'غزة', 'رام الله', 'الخليل', 'نابلس', 'بيت لحم', 'جنين', 'طولكرم',
            'قلقيلية', 'سلفيت', 'أريحا', 'طوباس', 'رفح', 'خان يونس', 'دير البلح',
            'بيت جالا', 'بيت ساحور', 'الناصرة', 'حيفا', 'يافا', 'عكا'
        ],
        'اليمن' => [
            'صنعاء', 'عدن', 'تعز', 'الحديدة', 'إب', 'ذمار', 'المكلا', 'سيئون',
            'زنجبار', 'الضالع', 'يريم', 'الباب', 'لحج', 'أبين', 'شبوة', 'مأرب',
            'الجوف', 'صعدة', 'حجة', 'المحويت', 'ريمة', 'عمران'
        ],
        'السودان' => [
            'الخرطوم', 'أم درمان', 'بورتسودان', 'الخرطوم بحري', 'الأبيض', 'نيالا',
            'الفاشر', 'القضارف', 'كسلا', 'الدمازين', 'ربك', 'سنار', 'كوستي',
            'عطبرة', 'مدني', 'الدويم', 'الرنك', 'ملكال', 'واو', 'جوبا'
        ],
        // إضافة دول جديدة
        'تركيا' => [
            'إسطنبول', 'أنقرة', 'إزمير', 'بورصة', 'أنطاليا', 'أضنة', 'غازي عنتاب',
            'قونيا', 'مرسين', 'ديار بكر', 'كايسري', 'إسكيشهير', 'طرابزون', 'شانلي أورفا',
            'ملاطيا', 'إرزوروم', 'وان', 'باتمان', 'إلازيغ', 'سيواس', 'ماردين'
        ],
        'إيران' => [
            'طهران', 'مشهد', 'أصفهان', 'كرج', 'تبريز', 'شيراز', 'أهواز', 'قم',
            'كرمانشاه', 'أروميه', 'رشت', 'زاهدان', 'هامادان', 'كرمان', 'يزد',
            'أردبيل', 'بندر عباس', 'إيلام', 'زنجان', 'سنندج', 'قزوين'
        ],
        'أفغانستان' => [
            'كابول', 'قندهار', 'هرات', 'مزار شريف', 'جلال آباد', 'كندوز', 'لشكر كاه',
            'طالقان', 'كوست', 'غزني', 'باميان', 'فيض آباد', 'شبرغان', 'ترمذ',
            'قلعة نو', 'چاريكار', 'ميمنة', 'أسد آباد', 'بول خمري', 'سر پل'
        ],
        'باكستان' => [
            'كراتشي', 'لاهور', 'فيصل آباد', 'راولبندي', 'ملتان', 'حيدر آباد', 'غوجرانوالا',
            'بيشاور', 'إسلام آباد', 'كويتا', 'سيالكوت', 'سكهر', 'لاركانا', 'شيخوبورا',
            'جهانغ', 'رحيم يار خان', 'فيصل آباد', 'ساهيوال', 'أوكارا', 'كامونكي'
        ],
        'بنغلاديش' => [
            'دكا', 'شيتاغونغ', 'خولنا', 'راجشاهي', 'سيلهت', 'رانغبور', 'بارسيال',
            'ميمنسينغ', 'كوميلا', 'نارايانغانج', 'غازيبور', 'تونغي', 'براهمانباريا',
            'جيسور', 'كوكس بازار', 'بوغرا', 'دينجبور', 'كيشورغانج', 'فريدبور'
        ],
        'إندونيسيا' => [
            'جاكرتا', 'سورابايا', 'باندونغ', 'بكانبارو', 'ميدان', 'تانغيرانغ', 'ديبوك',
            'سيمارانغ', 'باليمبانغ', 'مكاسار', 'باتام', 'بندر لامبونغ', 'بادانغ',
            'مالانغ', 'تاسيكمالايا', 'بانجارماسين', 'دنباسار', 'ساماريندا', 'جامبي'
        ],
        'ماليزيا' => [
            'كوالالمبور', 'جورج تاون', 'إيبوه', 'جوهور بهرو', 'ملقا', 'شاه علم',
            'كوتا كينابالو', 'كوتشينغ', 'بيتالينغ جايا', 'كلانغ', 'سوبانغ جايا',
            'كوانتان', 'سيريمبان', 'إسكندر بوتري', 'كوالا ترنغانو', 'ألور ستار'
        ]
    ];
    // التصنيفات الرئيسية والفرعية
    $mainCategories = [
        'vehicles' => [
            'name' => 'مركبات',
            'icon' => 'fa-car',
            'subcategories' => [
                'new_cars' => 'سيارات جديدة',
                'used_cars' => 'سيارات مستعملة',
                'motorcycles' => 'دراجات نارية',
                'car_parts' => 'قطع غيار وإكسسوارات'
            ]
        ],
        'realestate' => [
            'name' => 'عقارات',
            'icon' => 'fa-building',
            'subcategories' => [
                'apartments_sale' => 'شقق للبيع',
                'apartments_rent' => 'شقق للإيجار',
                'villas' => 'فلل ومنازل',
                'lands' => 'أراضي للبيع'
            ]
        ],
        'animals' => [
            'name' => 'مواشي وحيوانات',
            'icon' => 'fa-paw',
            'subcategories' => [
                'sheep' => 'أغنام',
                'cows' => 'أبقار',
                'camels' => 'إبل',
                'birds' => 'طيور زينة',
                'pets' => 'كلاب وقطط'
            ]
        ],
        'electronics' => [
            'name' => 'الكترونيات وتقنية',
            'icon' => 'fa-laptop',
            'subcategories' => [
                'phones' => 'جوالات',
                'computers' => 'كمبيوترات',
                'tvs' => 'شاشات وتلفزيونات',
                'cameras' => 'كاميرات مراقبة'
            ]
        ],
        'jobs' => [
            'name' => 'وظائف وأعمال',
            'icon' => 'fa-briefcase',
            'subcategories' => [
                'admin_jobs' => 'وظائف إدارية',
                'tech_jobs' => 'وظائف فنية وهندسية',
                'sales_jobs' => 'وظائف مبيعات وتسويق',
                'freelance' => 'أعمال حرة ومقاولات'
            ]
        ],
        'services' => [
            'name' => 'خدمات عامة',
            'icon' => 'fa-concierge-bell',
            'subcategories' => [
                'moving' => 'خدمات نقل عفش',
                'car_maintenance' => 'صيانة سيارات',
                'cleaning' => 'تنظيف منازل',
                'construction' => 'خدمات بناء وصيانة مباني'
            ]
        ],
        'furniture' => [
            'name' => 'أثاث ومستلزمات المنزل',
            'icon' => 'fa-couch',
            'subcategories' => [
                'bedrooms' => 'غرف نوم',
                'kitchens' => 'مطابخ',
                'office_furniture' => 'أثاث مكتبي',
                'home_appliances' => 'أدوات منزلية وكهربائية'
            ]
        ],
        'hobbies' => [
            'name' => 'هوايات وترفيه',
            'icon' => 'fa-gamepad',
            'subcategories' => [
                'sports' => 'أدوات رياضية',
                'games' => 'ألعاب إلكترونية',
                'books' => 'كتب ومجلات',
                'music' => 'آلات موسيقية'
            ]
        ],
        'fashion' => [
            'name' => 'ملابس وأزياء',
            'icon' => 'fa-tshirt',
            'subcategories' => [
                'men_clothes' => 'ملابس رجالية',
                'women_clothes' => 'ملابس نسائية',
                'shoes_bags' => 'أحذية وحقائب',
                'watches' => 'ساعات واكسسوارات'
            ]
        ],
        'other' => [
            'name' => 'أخرى',
            'icon' => 'fa-box',
            'subcategories' => [
                'medical' => 'مستلزمات طبية',
                'gifts' => 'تحف وهدايا',
                'events' => 'مستلزمات مناسبات'
            ]
        ]
    ];

    // للتوافق مع الكود القديم، نحتفظ بمصفوفة التصنيفات البسيطة
    $categories = [
        'cars' => 'سيارات',
        'realestate' => 'عقارات',
        'devices' => 'أجهزة',
        'animals' => 'حيوانات',
        'furniture' => 'اثاث',
        'jobs' => 'وظائف',
        'services' => 'خدمات',
        'fashion' => 'ازياء',
        'games' => 'العاب',
        'rarities' => 'نوادر',
        'art' => 'الفنون',
        'trips' => 'الرحلات',
        'food' => 'اطعمة',
        'gardens' => 'الحدائق',
        'occasions' => 'مناسبات',
        'tourism' => 'سياحة',
        'lost' => 'مفقودات',
        'coach' => 'تدريب',
        'code' => 'برمجة',
        'fund' => 'مشاريع واستثمارات',
        'more' => 'المزيد'
    ];
@endphp

<!-- قسم البحث - منصة انشر -->
<div class="container">
    @auth
        @php
            $userAdsCount = \App\Models\Ad::where('user_id', auth()->id())->count();
            $maxAdsAllowed = config('ads.max_ads_per_user', 2);
            $remainingAds = $maxAdsAllowed - $userAdsCount;
        @endphp

        <!-- رسالة تحذيرية حول الحد الأقصى للإعلانات -->
        @if($userAdsCount >= $maxAdsAllowed)
            <div class="alert alert-warning mb-4" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border: 1px solid #ffc107; border-radius: 12px; padding: 1rem; box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);">
                <div style="display: flex; align-items: center; gap: 0.75rem;">
                    <i class="fas fa-exclamation-triangle" style="color: #856404; font-size: 1.25rem;"></i>
                    <div style="flex: 1;">
                        <strong style="color: #856404;">تنبيه:</strong>
                        <span style="color: #856404;">لقد وصلت للحد الأقصى المسموح ({{ $maxAdsAllowed }} إعلانات). يرجى حذف أحد إعلاناتك الحالية لإضافة إعلان جديد.</span>
                    </div>
                    <a href="{{ route('dashboard') }}" style="background: #856404; color: white; padding: 0.5rem 1rem; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s ease;">
                        <i class="fas fa-cog" style="margin-left: 0.5rem;"></i>
                        إدارة إعلاناتي
                    </a>
                </div>
            </div>
        @elseif($remainingAds == 1)
            <div class="alert alert-info mb-4" style="background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); border: 1px solid #17a2b8; border-radius: 12px; padding: 1rem; box-shadow: 0 4px 12px rgba(23, 162, 184, 0.2);">
                <div style="display: flex; align-items: center; gap: 0.75rem;">
                    <i class="fas fa-info-circle" style="color: #0c5460; font-size: 1.25rem;"></i>
                    <div style="flex: 1;">
                        <strong style="color: #0c5460;">معلومة:</strong>
                        <span style="color: #0c5460;">يمكنك إضافة إعلان واحد فقط بعد هذا. الحد الأقصى المسموح هو {{ $maxAdsAllowed }} إعلانات لكل مستخدم.</span>
                    </div>
                    <div style="background: #0c5460; color: white; padding: 0.5rem 1rem; border-radius: 8px; font-weight: 600;">
                        {{ $userAdsCount }}/{{ $maxAdsAllowed }}
                    </div>
                </div>
            </div>
        @endif
    @endauth

    <!-- شريط البحث -->
    <div class="search-container">
        @auth
            @if($userAdsCount < $maxAdsAllowed)
                <a href="{{ route('ads.create') }}" class="add-ad-btn">
                    <i class="fas fa-plus"></i>
                    <span>إضافة عرض</span>
                </a>
            @else
                <div class="add-ad-btn" style="background-color: #6c757d; cursor: not-allowed; opacity: 0.6;" title="وصلت للحد الأقصى من الإعلانات">
                    <i class="fas fa-ban"></i>
                    <span>وصلت للحد الأقصى</span>
                </div>
            @endif
        @else
            <a href="{{ route('login') }}" class="add-ad-btn">
                <i class="fas fa-plus"></i>
                <span>إضافة عرض</span>
            </a>
        @endauth

        <form action="{{ route('ads.index') }}" method="GET" class="search-box flex-1">
         @csrf
        <input type="text" name="search" class="search-input" placeholder="ابحث عن سلعة" value="{{ request('search') }}">
         
            <button type="submit" class="search-btn">
                <i class="fas fa-search"></i>
            </button>
            <input type="hidden" name="category" value="{{ request('category') }}">
            <input type="hidden" name="location" value="{{ request('location') }}">
        </form>
    </div>

    <!-- الإعلانات الخاصة - أعلى الصفحة -->
    <div class="special-ads-app mt-4" data-position="top"></div>

    <!-- أيقونات التصنيفات الرئيسية مع شريط تمرير -->
    <div class="categories-container">
        <div class="scroll-arrow scroll-left" id="scrollLeft">
            <i class="fas fa-chevron-right"></i>
        </div>

        <div class="categories-scroll" id="categoriesScroll">
            <div class="categories">
                <a href="{{ route('ads.index') }}" class="category-item {{ !request('category') && !request('subcategory') ? 'active' : '' }}">
                    <div class="category-icon">
                        <i class="fas fa-th-large"></i>
                    </div>
                    <div class="category-name">الكل</div>
                </a>

                @foreach($mainCategories as $key => $mainCategory)
                    <a href="{{ route('ads.index', ['category' => $key]) }}"
                       class="category-item {{ request('category') == $key ? 'active' : '' }}"
                       data-category="{{ $key }}">
                        <div class="category-icon">
                            <i class="fas {{ $mainCategory['icon'] }}"></i>
                        </div>
                        <div class="category-name">{{ $mainCategory['name'] }}</div>
                    </a>
                @endforeach
            </div>
        </div>

        <div class="scroll-arrow scroll-right" id="scrollRight">
            <i class="fas fa-chevron-left"></i>
        </div>
    </div>

    <!-- التصنيفات الفرعية -->
    @foreach($mainCategories as $mainKey => $mainCategory)
        <div class="subcategories-container" id="subcategories-{{ $mainKey }}" style="{{ request('category') == $mainKey ? '' : 'display: none;' }}">
            <div class="subcategories-scroll">
                <div class="subcategories">
                    <a href="{{ route('ads.index', ['category' => $mainKey]) }}"
                       class="subcategory-item {{ request('category') == $mainKey && !request('subcategory') ? 'active' : '' }}">
                        <div class="subcategory-name">الكل</div>
                    </a>

                    @foreach($mainCategory['subcategories'] as $subKey => $subName)
                        <a href="{{ route('ads.index', ['category' => $mainKey, 'subcategory' => $subKey]) }}"
                           class="subcategory-item {{ request('subcategory') == $subKey ? 'active' : '' }}">
                            <div class="subcategory-name">{{ $subName }}</div>
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    @endforeach

    <!-- شريط التصفية والترتيب -->
    <div class="filter-bar">
        <div class="filter-options">
            <button class="filter-btn">
                <i class="fas fa-filter"></i>
                <span>تصفية</span>
            </button>

            <div class="sort-dropdown">
                <form action="{{ route('ads.index') }}" method="GET" class="sort-form">
                    <input type="hidden" name="search" value="{{ request('search') }}">
                    <input type="hidden" name="category" value="{{ request('category') }}">
                    <input type="hidden" name="location" value="{{ request('location') }}">

                    <select name="sort" id="sortOptions" class="sort-select" onchange="this.form.submit()">
                        <option value="" {{ !request('sort') ? 'selected' : '' }}>ترتيب حسب</option>
                        <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>الأحدث</option>
                        <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>الأقدم</option>
                        <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>السعر: من الأعلى</option>
                        <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>السعر: من الأقل</option>
                        <option value="title_asc" {{ request('sort') == 'title_asc' ? 'selected' : '' }}>العنوان: أ-ي</option>
                        <option value="nearest" {{ request('sort') == 'nearest' ? 'selected' : '' }}>الأقرب إليك</option>
                    </select>
                </form>
            </div>
        </div>

        <div class="location-filters">
            <div class="nearby-toggle-container">
                <label for="nearbyToggle" class="nearby-label">
                    <span>الإعلانات القريبة</span>
                    <span id="nearbyStatus" class="nearby-status">معطّل</span>
                    <div class="toggle-switch">
                        <input type="checkbox" id="nearbyToggle" class="toggle-input">
                        <span class="toggle-slider"></span>
                    </div>
                </label>
                <div id="locationLoading" class="location-loading" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
            </div>
            <button class="location-btn" id="locationBtn">
                <i class="fas fa-map-marker-alt"></i>
                <span id="currentLocation">تحديد الموقع</span>
            </button>
        </div>
    </div>

    <!-- رسائل الموقع والإعلانات القريبة -->
    <div id="locationError" class="alert alert-danger" style="display: none;"></div>
    <div id="nearbyError" class="alert alert-danger" style="display: none;"></div>
    <div id="nearbyMessage" class="alert alert-success" style="display: none;"></div>
    <div id="adsLoading" class="ads-loading" style="display: none;">
        <div class="loading-spinner"></div>
        <p>جاري تحميل الإعلانات القريبة...</p>
    </div>

    <!-- قائمة الإعلانات - Haraj Style -->
    <div class="ads-count mb-3">
        <span class="text-gray-700 font-semibold">{{ $ads->total() }} إعلان</span>
    </div>

    @if($ads->count() > 0)
        <!-- قائمة الإعلانات -->
        <div class="ads-list">
            @if($ads->count() >= 5 && $ads->currentPage() == 1)
                <!-- الإعلانات الخاصة - وسط الصفحة (بعد 5 إعلانات) -->
                <div class="special-ads-app my-4" data-position="middle"></div>
            @endif

            @foreach($ads as $index => $ad)
                <div class="ad-item bg-white rounded-lg overflow-hidden shadow-sm mb-3 hover:shadow-md transition-shadow duration-200 relative animate-fadeInUp animate-delay-{{ $index % 5 + 1 }}">
                    <!-- رابط كامل البطاقة -->
                    <a href="{{ route('ads.show', $ad->id) }}" class="absolute inset-0 z-10 cursor-pointer" aria-label="{{ $ad->title }}"></a>

                    <div class="flex flex-col md:flex-row h-full">
                        <!-- صورة الإعلان -->
                        <div class="ad-image w-full md:w-1/4 h-36 md:h-32 relative overflow-hidden">
                            <img src="{{ $ad->getImageUrl() }}" alt="{{ $ad->title }}" class="w-full h-full object-cover object-center">
                            @if($ad->is_featured)
                                <div class="absolute top-2 right-2 bg-yellow-400 text-white text-xs px-2 py-1 rounded z-20 animate-pulse">مميز</div>
                            @endif
                        </div>

                        <!-- تفاصيل الإعلان -->
                        <div class="ad-details p-3 flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="text-base font-bold text-gray-800 mb-1 line-clamp-1">{{ $ad->title }}</h3>
                                <div class="text-green-600 font-bold text-sm">
                                    @if($ad->price)
                                        {{ $ad->price }} ريال
                                    @endif
                                </div>
                            </div>

                            <div class="flex items-center text-gray-500 text-xs mb-1">
                                <div class="flex items-center ml-3">
                                    <i class="fas fa-map-marker-alt ml-1" style="color: #E67E22;"></i>
                                    <span>{{ $ad->location ?? 'غير محددة' }}</span>
                                </div>
                                <div class="flex items-center ml-3">
                                    <i class="far fa-clock ml-1" style="color: #E67E22;"></i>
                                    <span>{{ $ad->created_at->diffForHumans() }}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-eye ml-1" style="color: #E67E22;"></i>
                                    <span>{{ $ad->getFormattedViews() }}</span>
                                </div>
                            </div>

                            <div class="mb-2 text-xs text-gray-600 line-clamp-1">
                                {{ Str::limit($ad->description, 100) }}
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-gray-700 font-bold ml-1 text-xs">
                                        {{ substr($ad->user->name ?? 'م', 0, 1) }}
                                    </div>
                                    <span class="text-xs text-gray-700">{{ $ad->user->name ?? 'مستخدم' }}</span>
                                </div>

                                <div class="flex gap-2 relative z-20">
                                    @if (auth()->id() === $ad->user_id)
                                        <a href="{{ route('ads.edit', $ad->id) }}" class="text-yellow-500 hover:text-yellow-700 text-sm p-1" onclick="event.stopPropagation();">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <form action="{{ route('ads.destroy', $ad->id) }}" method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الإعلان؟ لا يمكن التراجع عن هذه العملية.') && event.stopPropagation();">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-500 hover:text-red-700 bg-transparent border-0 text-sm p-1" onclick="event.stopPropagation();">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Car Brand Logos - منصة انشر -->
        <div class="car-brands grid grid-cols-5 gap-4 my-6">
            <a href="#" class="brand-item flex justify-center items-center bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                <img src="https://upload.wikimedia.org/wikipedia/commons/4/44/BMW.svg" alt="BMW" class="h-12">
            </a>
            <a href="#" class="brand-item flex justify-center items-center bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                <img src="https://upload.wikimedia.org/wikipedia/commons/4/4d/Toyota_Motor_Corporation_logo.svg" alt="Toyota" class="h-12">
            </a>
            <a href="#" class="brand-item flex justify-center items-center bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                <img src="https://upload.wikimedia.org/wikipedia/commons/6/6d/Nissan_logo.svg" alt="Nissan" class="h-12">
            </a>
            <a href="#" class="brand-item flex justify-center items-center bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                <img src="https://upload.wikimedia.org/wikipedia/commons/4/44/Mercedes-Benz_Logo.svg" alt="Mercedes" class="h-12">
            </a>
            <a href="#" class="brand-item flex justify-center items-center bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                <img src="https://upload.wikimedia.org/wikipedia/commons/a/a0/Lexus_logo.svg" alt="Lexus" class="h-12">
            </a>
        </div>
    @else
        <!-- حالة لا توجد إعلانات -->
        <div class="empty-state bg-white rounded-lg p-8 text-center shadow-sm">
            <i class="fas fa-search text-4xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">لا توجد إعلانات متاحة</h3>
            <p class="text-gray-500 mb-4">جرب تعديل معايير البحث أو تصفح جميع الإعلانات</p>
            <a href="{{ route('ads.index') }}" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md inline-block transition-colors duration-200">
                عرض جميع الإعلانات
            </a>
        </div>
    @endif



    <!-- الإعلانات الخاصة - أسفل الصفحة -->
    <div class="special-ads-app my-4" data-position="bottom"></div>

    <!-- ترقيم الصفحات - منصة انشر -->
    <div class="pagination">
        <div class="pagination-inner">
            <!-- زر الصفحة الأولى -->
            <a href="{{ $ads->url(1) }}" class="pagination-btn {{ $ads->onFirstPage() ? 'disabled' : '' }} pagination-mobile-hidden">
                <i class="fas fa-angle-double-right ml-1"></i>
                <span>الصفحة الأولى</span>
            </a>

            <!-- السابق -->
            <a href="{{ $ads->previousPageUrl() }}" class="pagination-btn {{ $ads->onFirstPage() ? 'disabled' : '' }}" aria-label="الصفحة السابقة">
                <i class="fas fa-chevron-right ml-2"></i>
                <span>السابق</span>
            </a>

            <!-- الأرقام -->
            <div class="pagination-numbers">
                @if($ads->hasPages())
                    @if($ads->currentPage() > 3)
                        <a href="{{ $ads->url(1) }}" class="pagination-number pagination-mobile-hidden">1</a>
                        @if($ads->currentPage() > 4)
                            <span class="pagination-dots pagination-mobile-hidden">...</span>
                        @endif
                    @endif

                    @for($i = max(1, $ads->currentPage() - 2); $i <= min($ads->lastPage(), $ads->currentPage() + 2); $i++)
                        <a href="{{ $ads->url($i) }}" class="pagination-number {{ $ads->currentPage() == $i ? 'active' : '' }}" aria-label="الصفحة {{ $i }}">
                            {{ $i }}
                        </a>
                    @endfor

                    @if($ads->currentPage() < $ads->lastPage() - 2)
                        @if($ads->currentPage() < $ads->lastPage() - 3)
                            <span class="pagination-dots pagination-mobile-hidden">...</span>
                        @endif
                        <a href="{{ $ads->url($ads->lastPage()) }}" class="pagination-number pagination-mobile-hidden">{{ $ads->lastPage() }}</a>
                    @endif
                @else
                    <!-- عرض ترقيم توضيحي في حالة عدم وجود صفحات متعددة -->
                    <a href="javascript:void(0)" class="pagination-number active" aria-label="الصفحة 1">1</a>
                    <a href="javascript:void(0)" class="pagination-number disabled" aria-label="الصفحة 2">2</a>
                    <a href="javascript:void(0)" class="pagination-number disabled" aria-label="الصفحة 3">3</a>
                @endif
            </div>

            <!-- التالي -->
            <a href="{{ $ads->nextPageUrl() }}" class="pagination-btn {{ !$ads->hasMorePages() ? 'disabled' : '' }}" aria-label="الصفحة التالية">
                <span>التالي</span>
                <i class="fas fa-chevron-left mr-2"></i>
            </a>

            <!-- زر الصفحة الأخيرة -->
            <a href="{{ $ads->url($ads->hasPages() ? $ads->lastPage() : 1) }}" class="pagination-btn {{ !$ads->hasMorePages() ? 'disabled' : '' }} pagination-mobile-hidden">
                <span>الصفحة الأخيرة</span>
                <i class="fas fa-angle-double-left mr-1"></i>
            </a>
        </div>

        <div class="pagination-info">
            @if($ads->total() > 0)
                عرض {{ $ads->firstItem() ?? 0 }} - {{ $ads->lastItem() ?? 0 }} من إجمالي {{ $ads->total() }} إعلان
            @else
                لا توجد إعلانات متاحة حاليًا
            @endif
        </div>
    </div>
</div>

<!-- Footer -->
<footer>
    <div class="container">
        <div class="footer-content">
            <div class="footer-column">
                <h3>منصة انشر</h3>
                <p>منصتك الأولى للإعلانات المبوبة في الوطن العربي</p>
                <div class="social-icons mt-4 flex gap-3">
                    <a href="#" class="text-white hover:text-yellow-400 transition-colors"><i class="fab fa-facebook-f" style="color: #E67E22;"></i></a>
                    <a href="#" class="text-white hover:text-yellow-400 transition-colors"><i class="fab fa-twitter" style="color: #E67E22;"></i></a>
                    <a href="#" class="text-white hover:text-yellow-400 transition-colors"><i class="fab fa-instagram" style="color: #E67E22;"></i></a>
                    <a href="#" class="text-white hover:text-yellow-400 transition-colors"><i class="fab fa-snapchat-ghost" style="color: #E67E22;"></i></a>
                </div>
            </div>
            <div class="footer-column">
                <h3>روابط سريعة</h3>
                <ul class="footer-links">
                    <li><a href=" ">الرئيسية</a></li>
                    <li><a href=" ">تصفح الإعلانات</a></li>
                    <li><a href=" ">الفئات</a></li>
                    <li><a href=" ">من نحن</a></li>
                    <li><a href=" ">اتصل بنا</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>فئات الإعلانات</h3>
                <ul class="footer-links">
                    <li><a href="{{ route('ads.index', ['category' => 'cars']) }}">سيارات</a></li>
                    <li><a href="{{ route('ads.index', ['category' => 'realestate']) }}">عقارات</a></li>
                    <li><a href="{{ route('ads.index', ['category' => 'devices']) }}">أجهزة</a></li>
                    <li><a href="{{ route('ads.index', ['category' => 'animals']) }}">حيوانات</a></li>
                    <li><a href="{{ route('ads.index', ['category' => 'furniture']) }}">اثاث</a></li>
                    <li><a href="{{ route('ads.index', ['category' => 'jobs']) }}">وظائف</a></li>
                    <li><a href="{{ route('ads.index', ['category' => 'services']) }}">خدمات</a></li>
                    <li><a href="{{ route('ads.index', ['category' => 'more']) }}">المزيد</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>خدمات المنصة</h3>
                <ul class="footer-links">
                    <li><a href=" ">نشر إعلان</a></li>
                    <li><a href=" ">الإعلانات المميزة</a></li>
                    <li><a href=" ">شراء النقاط</a></li>
                    <li><a href=" ">الباقات والأسعار</a></li>
                    <li><a href="{{ route('privacy') }}" >سياسة الخصوصية</a></li>
                    <li><a href="{{ route('terms-and-conditions') }}">الشروط والأحكام</a></li>
                    <li><a href=" ">الأسئلة الشائعة</a></li>
                    <li><a href=" ">اتصل بنا</a></li>
                </ul>
            </div>
        </div>
        <div class="footer-bottom">
            <p>جميع الحقوق محفوظة &copy; {{ date('Y') }} منصة انشر للإعلانات المبوبة - الوطن العربي</p>
        </div>
    </div>
</footer>

<style>
    /* أنماط عرض الصور والبطاقات */
    .ad-item {
        transition: all 0.2s ease;
        border: 1px solid #eee;
        cursor: pointer;
    }

    .ad-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #ddd;
    }

    .ad-item:active {
        transform: translateY(0);
        transition: transform 0.1s;
    }

    .ad-image {
        position: relative;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .ad-image img {
        transition: transform 0.3s ease;
        max-height: 100%;
        max-width: 100%;
    }

    .ad-image:hover img {
        transform: scale(1.05);
    }

    .line-clamp-1 {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    @media (max-width: 768px) {
        .ad-image {
            height: 160px;
        }
    }

    @media (min-width: 769px) {
        .ad-item {
            max-height: 140px;
        }
    }

    /* Pagination Styles - منصة انشر */
    .pagination {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        padding: 15px;
        margin: 30px 0;
        position: relative;
        overflow: hidden;
    }

    .pagination::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        height: 3px;
        background: linear-gradient(to left, #0066cc, #4da6ff);
    }

    .pagination-inner {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .pagination-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        background-color: #f8f9fa;
        color: #0066cc;
        border: 1px solid #e9ecef;
        border-radius: 50px;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;
        min-width: 100px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .pagination-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to right, rgba(0, 102, 204, 0.1), rgba(0, 102, 204, 0));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .pagination-btn:hover:not(.disabled) {
        background-color: #0066cc;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 102, 204, 0.2);
    }

    .pagination-btn:hover:not(.disabled)::before {
        opacity: 1;
    }

    .pagination-btn.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: #f1f3f5;
        color: #adb5bd;
        border-color: #e9ecef;
    }

    .pagination-btn i {
        transition: transform 0.3s ease;
    }

    .pagination-btn:hover:not(.disabled) i.fa-chevron-right {
        transform: translateX(-3px);
    }

    .pagination-btn:hover:not(.disabled) i.fa-chevron-left {
        transform: translateX(3px);
    }

    .pagination-numbers {
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .pagination-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background-color: #f8f9fa;
        color: #0066cc;
        border: 1px solid #e9ecef;
        border-radius: 50%;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .pagination-number:hover:not(.active) {
        background-color: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .pagination-number.active {
        background-color: #0066cc;
        color: white;
        border-color: #0066cc;
        box-shadow: 0 5px 15px rgba(0, 102, 204, 0.3);
        transform: scale(1.1);
    }

    .pagination-number.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: #f1f3f5;
        color: #adb5bd;
        border-color: #e9ecef;
        pointer-events: none;
    }

    .pagination-dots {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        color: #6c757d;
        font-size: 14px;
    }

    .pagination-info {
        text-align: center;
        margin-top: 10px;
        font-size: 13px;
        color: #6c757d;
    }

    @media (max-width: 768px) {
        .pagination {
            padding: 12px 10px;
        }

        .pagination-inner {
            flex-wrap: wrap;
        }

        .pagination-btn {
            min-width: auto;
            padding: 8px 12px;
            font-size: 13px;
        }

        .pagination-number {
            width: 32px;
            height: 32px;
            font-size: 13px;
        }

        .pagination-mobile-hidden {
            display: none;
        }
    }

    /* Footer Styles */
    footer {
        background: linear-gradient(to right, #1e293b, #0f172a);
        color: var(--text-white);
        padding: 4rem 0 2rem;
        margin-top: 4rem;
        position: relative;
        overflow: hidden;
    }

    footer::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(to right, var(--primary-light), var(--accent-color));
    }

    .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 3rem;
    }

    .footer-column h3 {
        margin-bottom: 1.5rem;
        color: var(--text-white);
        font-size: 1.3rem;
        position: relative;
        padding-bottom: 0.75rem;
    }

    .footer-column h3::after {
        content: "";
        position: absolute;
        bottom: 0;
        right: 0;
        width: 50px;
        height: 3px;
        background-color: var(--accent-color);
        border-radius: 2px;
    }

    .footer-links {
        list-style: none;
    }

    .footer-links li {
        margin-bottom: 0.75rem;
    }

    .footer-links a {
        color: #cbd5e1;
        text-decoration: none;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .footer-links a::before {
        content: "\f054";
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
        font-size: 0.7rem;
        color: var(--accent-color);
    }

    .footer-links a:hover {
        color: var(--text-white);
        transform: translateX(-5px);
    }

    .footer-bottom {
        margin-top: 3rem;
        text-align: center;
        padding-top: 2rem;
        border-top: 1px solid #334155;
        color: #94a3b8;
    }

    .social-icons {
        display: flex;
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .social-icons a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .social-icons a:hover {
        background-color: var(--accent-color);
        color: #0f172a !important;
        transform: translateY(-3px);
    }

    /* أنماط الإعلانات القريبة */
    .nearby-toggle-container {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-left: 15px;
    }

    .nearby-label {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        font-size: 14px;
    }

    .nearby-status {
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 4px;
        background-color: #f1f3f5;
        color: #868e96;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 40px;
        height: 20px;
    }

    .toggle-input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    .toggle-input:checked + .toggle-slider {
        background-color: var(--primary-color);
    }

    .toggle-input:checked + .toggle-slider:before {
        transform: translateX(20px);
    }

    .location-loading {
        display: inline-block;
        color: var(--primary-color);
        font-size: 14px;
        animation: spin 1s infinite linear;
    }

    .location-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .location-btn:hover {
        background-color: #e9ecef;
    }

    .alert {
        padding: 10px 15px;
        margin: 10px 0;
        border-radius: 4px;
        font-size: 14px;
    }

    .alert-danger {
        background-color: #fff5f5;
        color: #e03131;
        border: 1px solid #ffc9c9;
    }

    .alert-success {
        background-color: #ebfbee;
        color: #2b8a3e;
        border: 1px solid #b2f2bb;
    }

    .ads-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin: 20px 0;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #0066cc;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

    <script>
        // متغيرات عالمية للموقع الجغرافي
        let userLatitude = null;
        let userLongitude = null;
        let userLocationName = '';
        let isNearbyEnabled = localStorage.getItem('nearbyAdsEnabled') === 'true';

        // دالة للحصول على موقع المستخدم
        function getUserLocation() {
            if (navigator.geolocation) {
                // إظهار مؤشر التحميل
                showLocationLoading(true);

                navigator.geolocation.getCurrentPosition(
                    // نجاح الحصول على الموقع
                    function(position) {
                        userLatitude = position.coords.latitude;
                        userLongitude = position.coords.longitude;

                        // تخزين الإحداثيات في localStorage
                        localStorage.setItem('userLatitude', userLatitude);
                        localStorage.setItem('userLongitude', userLongitude);

                        // الحصول على اسم الموقع باستخدام Reverse Geocoding
                        reverseGeocode(userLatitude, userLongitude);

                        // إخفاء مؤشر التحميل
                        showLocationLoading(false);

                        // تحديث واجهة المستخدم
                        updateNearbyUI(true);

                        // جلب الإعلانات القريبة
                        if (isNearbyEnabled) {
                            fetchNearbyAds();
                        }
                    },
                    // فشل الحصول على الموقع
                    function(error) {
                        console.error("خطأ في الحصول على الموقع:", error.message);
                        showLocationError(error.message);
                        showLocationLoading(false);
                        updateNearbyUI(false);
                    },
                    // خيارات الموقع
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 600000 // 10 دقائق
                    }
                );
            } else {
                console.error("المتصفح لا يدعم تحديد الموقع الجغرافي");
                showLocationError("المتصفح لا يدعم تحديد الموقع الجغرافي");
                updateNearbyUI(false);
            }
        }

        // دالة للحصول على اسم الموقع من الإحداثيات
        function reverseGeocode(lat, lng) {
            // استخدام خدمة Nominatim OpenStreetMap للحصول على اسم الموقع
            fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=ar`)
                .then(response => response.json())
                .then(data => {
                    if (data && data.address) {
                        // محاولة الحصول على اسم المدينة أو المنطقة
                        const city = data.address.city || data.address.town || data.address.village || data.address.county || '';
                        const country = data.address.country || '';

                        if (city && country) {
                            userLocationName = `${country} - ${city}`;
                            localStorage.setItem('userLocationName', userLocationName);

                            // تحديث واجهة المستخدم بالموقع
                            updateLocationDisplay(userLocationName);

                            // إذا كان تفعيل الإعلانات القريبة نشطًا، قم بجلب الإعلانات
                            if (isNearbyEnabled) {
                                fetchNearbyAds();
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error("خطأ في الحصول على اسم الموقع:", error);
                });
        }

        // دالة لجلب الإعلانات القريبة
        function fetchNearbyAds() {
            if (!userLatitude || !userLongitude) return;

            // إنشاء نموذج بيانات لإرسال الإحداثيات
            const formData = new FormData();
            formData.append('latitude', userLatitude);
            formData.append('longitude', userLongitude);
            formData.append('location_name', userLocationName);

            // إضافة رمز CSRF
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            // إظهار مؤشر التحميل
            showAdsLoading(true);

            // إرسال الطلب إلى الخادم
            fetch('{{ route("ads.nearby") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث قائمة الإعلانات
                    updateAdsList(data.ads);
                    showNearbyMessage(data.message);
                } else {
                    console.error("خطأ في جلب الإعلانات القريبة:", data.message);
                    showNearbyError(data.message);
                }
                showAdsLoading(false);
            })
            .catch(error => {
                console.error("خطأ في الاتصال بالخادم:", error);
                showNearbyError("حدث خطأ في الاتصال بالخادم");
                showAdsLoading(false);
            });
        }

        // دالة لتحديث واجهة المستخدم بناءً على حالة الموقع
        function updateNearbyUI(isEnabled) {
            const nearbyToggle = document.getElementById('nearbyToggle');
            if (nearbyToggle) {
                nearbyToggle.checked = isEnabled;
                document.getElementById('nearbyStatus').textContent = isEnabled ? 'مفعّل' : 'معطّل';
            }
        }

        // دالة لإظهار/إخفاء مؤشر تحميل الموقع
        function showLocationLoading(show) {
            const locationLoading = document.getElementById('locationLoading');
            if (locationLoading) {
                locationLoading.style.display = show ? 'inline-block' : 'none';
            }
        }

        // دالة لإظهار/إخفاء مؤشر تحميل الإعلانات
        function showAdsLoading(show) {
            const adsLoading = document.getElementById('adsLoading');
            if (adsLoading) {
                adsLoading.style.display = show ? 'block' : 'none';
            }
        }

        // دالة لإظهار رسالة خطأ في الموقع
        function showLocationError(message) {
            const locationError = document.getElementById('locationError');
            if (locationError) {
                locationError.textContent = message;
                locationError.style.display = 'block';
                setTimeout(() => {
                    locationError.style.display = 'none';
                }, 5000);
            }
        }

        // دالة لإظهار رسالة خطأ في جلب الإعلانات
        function showNearbyError(message) {
            const nearbyError = document.getElementById('nearbyError');
            if (nearbyError) {
                nearbyError.textContent = message;
                nearbyError.style.display = 'block';
                setTimeout(() => {
                    nearbyError.style.display = 'none';
                }, 5000);
            }
        }

        // دالة لإظهار رسالة نجاح جلب الإعلانات القريبة
        function showNearbyMessage(message) {
            const nearbyMessage = document.getElementById('nearbyMessage');
            if (nearbyMessage) {
                nearbyMessage.textContent = message;
                nearbyMessage.style.display = 'block';
                setTimeout(() => {
                    nearbyMessage.style.display = 'none';
                }, 5000);
            }
        }

        // دالة لتحديث عرض الموقع الحالي
        function updateLocationDisplay(locationName) {
            const currentLocation = document.getElementById('currentLocation');
            if (currentLocation) {
                currentLocation.textContent = locationName;
            }
        }

        // دالة لتحديث قائمة الإعلانات
        function updateAdsList(ads) {
            const adsContainer = document.getElementById('adsContainer');
            if (!adsContainer || !ads || ads.length === 0) return;

            // تفريغ الحاوية
            adsContainer.innerHTML = '';

            // إضافة الإعلانات الجديدة
            ads.forEach((ad, index) => {
                const adElement = createAdElement(ad, index);
                adsContainer.appendChild(adElement);
            });
        }

        // دالة لإنشاء عنصر إعلان
        function createAdElement(ad, index) {
            const adDiv = document.createElement('div');
            adDiv.className = `ad-item bg-white rounded-lg overflow-hidden shadow-sm mb-3 hover:shadow-md transition-shadow duration-200 relative animate-fadeInUp animate-delay-${index % 5 + 1}`;

            // إنشاء محتوى الإعلان (يجب تعديله حسب هيكل الإعلانات لديك)
            adDiv.innerHTML = `
                <a href="/ads/${ad.id}" class="absolute inset-0 z-10 cursor-pointer" aria-label="${ad.title}"></a>
                <div class="flex flex-col md:flex-row h-full">
                    <div class="ad-image w-full md:w-1/4 h-36 md:h-32 relative overflow-hidden">
                        <img src="${ad.image_url}" alt="${ad.title}" class="w-full h-full object-cover object-center">
                        ${ad.is_featured ? '<div class="absolute top-2 right-2 bg-yellow-400 text-white text-xs px-2 py-1 rounded z-20 animate-pulse">مميز</div>' : ''}
                    </div>
                    <div class="ad-details p-3 flex-1">
                        <div class="flex justify-between items-start">
                            <h3 class="text-base font-bold text-gray-800 mb-1 line-clamp-1">${ad.title}</h3>
                            <div class="text-green-600 font-bold text-sm">
                                ${ad.price ? ad.price + ' ريال' : ''}
                            </div>
                        </div>
                        <div class="flex items-center text-gray-500 text-xs mb-1">
                            <div class="flex items-center ml-3">
                                <i class="fas fa-map-marker-alt ml-1 text-blue-500"></i>
                                <span>${ad.location || 'غير محددة'}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="far fa-clock ml-1 text-blue-500"></i>
                                <span>${ad.created_at_human}</span>
                            </div>
                        </div>
                        <div class="mb-2 text-xs text-gray-600 line-clamp-1">
                            ${ad.description_short}
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-gray-700 font-bold ml-1 text-xs">
                                    ${ad.user_name ? ad.user_name.charAt(0) : 'م'}
                                </div>
                                <span class="text-xs text-gray-700">${ad.user_name || 'مستخدم'}</span>
                            </div>
                            <div class="flex gap-2 relative z-20">
                                ${ad.distance ? '<span class="text-xs text-blue-600"><i class="fas fa-location-arrow ml-1"></i>' + ad.distance + ' كم</span>' : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return adDiv;
        }

        // تنفيذ عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // استرجاع الإحداثيات المخزنة
            userLatitude = localStorage.getItem('userLatitude');
            userLongitude = localStorage.getItem('userLongitude');
            userLocationName = localStorage.getItem('userLocationName');

            // تحديث واجهة المستخدم
            if (userLocationName) {
                updateLocationDisplay(userLocationName);
            }

            updateNearbyUI(isNearbyEnabled);

            // إذا كانت ميزة الإعلانات القريبة مفعلة وتوجد إحداثيات مخزنة
            if (isNearbyEnabled && userLatitude && userLongitude) {
                fetchNearbyAds();
            }

            // إذا لم تكن هناك إحداثيات مخزنة أو مر وقت طويل، احصل على الموقع
            const lastLocationTime = localStorage.getItem('lastLocationTime');
            const now = Date.now();
            if (!userLatitude || !userLongitude || !lastLocationTime || (now - lastLocationTime > 3600000)) { // 1 ساعة
                getUserLocation();
                localStorage.setItem('lastLocationTime', now);
            }

            // تفعيل زر التبديل للإعلانات القريبة
            const nearbyToggle = document.getElementById('nearbyToggle');
            if (nearbyToggle) {
                // تعيين الحالة الأولية
                nearbyToggle.checked = isNearbyEnabled;

                // إضافة مستمع الحدث
                nearbyToggle.addEventListener('change', function() {
                    isNearbyEnabled = this.checked;
                    localStorage.setItem('nearbyAdsEnabled', isNearbyEnabled);

                    // تحديث واجهة المستخدم
                    updateNearbyUI(isNearbyEnabled);

                    if (isNearbyEnabled) {
                        // إذا كانت الإحداثيات متاحة، جلب الإعلانات القريبة
                        if (userLatitude && userLongitude) {
                            fetchNearbyAds();
                        } else {
                            // إذا لم تكن الإحداثيات متاحة، الحصول على الموقع
                            getUserLocation();
                        }
                    } else {
                        // إعادة تحميل الصفحة لعرض الإعلانات العادية
                        window.location.reload();
                    }
                });
            }

            // تفعيل زر الموقع
            const locationBtn = document.getElementById('locationBtn');
            if (locationBtn) {
                locationBtn.addEventListener('click', function() {
                    getUserLocation();
                });
            }

            // تفعيل زر التصفية
            const filterBtn = document.querySelector('.filter-btn');
            if (filterBtn) {
                filterBtn.addEventListener('click', function() {
                    // إنشاء نافذة منبثقة للتصفية
                    const filterModal = document.createElement('div');
                    filterModal.className = 'filter-modal';
                    filterModal.innerHTML = `
                        <div class="filter-modal-content">
                            <div class="filter-modal-header">
                                <h3>خيارات التصفية</h3>
                                <button class="close-modal">&times;</button>
                            </div>
                            <div class="filter-modal-body">
                                <form action="{{ route('ads.index') }}" method="GET">
                                    <input type="hidden" name="sort" value="{{ request('sort') }}">

                                    <div class="filter-group">
                                        <label>الفئة</label>
                                        <select name="category" class="filter-select">
                                            <option value="">كل الفئات</option>
                                            @foreach($categories as $key => $category)
                                                <option value="{{ $key }}" {{ request('category') == $key ? 'selected' : '' }}>
                                                    {{ $category }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <div class="filter-group">
                                        <label>الموقع</label>
                                        <select name="location" class="filter-select">
                                            <option value="">كل المواقع</option>
                                            @foreach($cities as $country => $countryLocations)
                                                <optgroup label="{{ $country }}">
                                                    @foreach($countryLocations as $city)
                                                        <option value="{{ $country }} - {{ $city }}" {{ request('location') == $country.' - '.$city ? 'selected' : '' }}>
                                                            {{ $city }}
                                                        </option>
                                                    @endforeach
                                                </optgroup>
                                            @endforeach
                                        </select>
                                    </div>

                                    <div class="filter-group">
                                        <label>السعر</label>
                                        <div class="price-range">
                                            <input type="number" name="min_price" placeholder="من" value="{{ request('min_price') }}" class="price-input">
                                            <span>-</span>
                                            <input type="number" name="max_price" placeholder="إلى" value="{{ request('max_price') }}" class="price-input">
                                        </div>
                                    </div>

                                    <div class="filter-actions">
                                        <button type="submit" class="apply-filter">تطبيق</button>
                                        <a href="{{ route('ads.index') }}" class="reset-filter">إعادة ضبط</a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    `;

                    document.body.appendChild(filterModal);

                    // إغلاق النافذة المنبثقة
                    const closeBtn = filterModal.querySelector('.close-modal');
                    closeBtn.addEventListener('click', function() {
                        document.body.removeChild(filterModal);
                    });

                    // إغلاق النافذة عند النقر خارجها
                    filterModal.addEventListener('click', function(e) {
                        if (e.target === filterModal) {
                            document.body.removeChild(filterModal);
                        }
                    });
                });
            }

            // تفعيل زر تبديل الوضع الليلي
            const themeToggle = document.querySelector('.theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    document.body.classList.toggle('dark-mode');
                });
            }

            // تفعيل الترتيب التلقائي
            const sortSelect = document.getElementById('sortOptions');
            if (sortSelect) {
                sortSelect.addEventListener('change', function() {
                    this.form.submit();
                });
            }
        });
    </script>

    <style>
        /* أنماط نافذة التصفية المنبثقة */
        .filter-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .filter-modal-content {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .filter-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }

        .filter-modal-header h3 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #777;
        }

        .filter-modal-body {
            padding: 20px;
        }

        .filter-group {
            margin-bottom: 20px;
        }

        .filter-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .filter-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            max-height: 300px;
        }

        .filter-select optgroup {
            font-weight: bold;
            color: #0066cc;
            background-color: #f8f9fa;
            padding: 5px;
        }

        .filter-select option {
            padding: 8px;
            background-color: white;
        }

        .price-range {
            display: flex;
            align-items: center;
            gap: 10px;http://127.0.0.1:8000/ads?category=animals
        }

        .price-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .filter-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .apply-filter {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }

        .reset-filter {
            color: #666;
            text-decoration: none;
            padding: 10px 20px;
        }
    </style>

    <!-- JavaScript للتمرير الأفقي وتأثيرات الحركة -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // دالة مساعدة للتحقق من اتجاه الصفحة (RTL أو LTR)
            function isRTL() {
                return window.getComputedStyle(document.body).direction === 'rtl';
            }

            // دالة مساعدة لتحديد اتجاه التمرير المناسب
            function getScrollDirection(amount) {
                // في وضع RTL، نعكس اتجاه التمرير
                return isRTL() ? amount : -amount;
            }
            // تهيئة شريط التمرير الأفقي للفئات
            const scrollContainer = document.getElementById('categoriesScroll');
            const scrollLeftBtn = document.getElementById('scrollLeft');
            const scrollRightBtn = document.getElementById('scrollRight');

            if (scrollContainer && scrollLeftBtn && scrollRightBtn) {
                // التمرير بالأزرار - استخدام الدوال المساعدة لتحديد الاتجاه الصحيح
                scrollLeftBtn.addEventListener('click', function() {
                    // زر اليسار يمرر في الاتجاه المعاكس لاتجاه النص
                    scrollContainer.scrollBy({ left: getScrollDirection(-200), behavior: 'smooth' });
                });

                scrollRightBtn.addEventListener('click', function() {
                    // زر اليمين يمرر في نفس اتجاه النص
                    scrollContainer.scrollBy({ left: getScrollDirection(200), behavior: 'smooth' });
                });

                // إخفاء/إظهار أزرار التمرير حسب الحاجة - يعمل مع RTL و LTR
                function updateScrollButtons() {
                    const maxScroll = scrollContainer.scrollWidth - scrollContainer.clientWidth;
                    let currentScroll, atStart, atEnd;

                    if (isRTL()) {
                        // في RTL، scrollLeft يكون سالبًا عندما نمرر لليمين
                        currentScroll = Math.abs(scrollContainer.scrollLeft);
                        atStart = currentScroll <= 0;
                        atEnd = currentScroll >= maxScroll - 10;
                    } else {
                        // في LTR، scrollLeft يكون موجبًا عندما نمرر لليمين
                        currentScroll = scrollContainer.scrollLeft;
                        atStart = currentScroll <= 0;
                        atEnd = currentScroll >= maxScroll - 10;
                    }

                    // تحديث زر التمرير الأيسر
                    if ((isRTL() && atEnd) || (!isRTL() && atStart)) {
                        scrollLeftBtn.style.opacity = '0.5';
                        scrollLeftBtn.style.pointerEvents = 'none';
                    } else {
                        scrollLeftBtn.style.opacity = '1';
                        scrollLeftBtn.style.pointerEvents = 'auto';
                    }

                    // تحديث زر التمرير الأيمن
                    if ((isRTL() && atStart) || (!isRTL() && atEnd)) {
                        scrollRightBtn.style.opacity = '0.5';
                        scrollRightBtn.style.pointerEvents = 'none';
                    } else {
                        scrollRightBtn.style.opacity = '1';
                        scrollRightBtn.style.pointerEvents = 'auto';
                    }
                }

                // تحديث حالة الأزرار عند التمرير
                scrollContainer.addEventListener('scroll', updateScrollButtons);

                // تحديث حالة الأزرار عند تحميل الصفحة
                updateScrollButtons();

                // تحديث حالة الأزرار عند تغيير حجم النافذة
                window.addEventListener('resize', updateScrollButtons);

                // تأكد من تحديث حالة الأزرار بعد تحميل جميع الصور
                window.addEventListener('load', updateScrollButtons);

                // تمرير الفئات باستخدام الماوس (سحب وإفلات) - تحسين للغة العربية RTL
                let isDown = false;
                let startX;
                let scrollLeft;

                scrollContainer.addEventListener('mousedown', (e) => {
                    isDown = true;
                    scrollContainer.style.cursor = 'grabbing';
                    startX = e.pageX - scrollContainer.offsetLeft;
                    scrollLeft = scrollContainer.scrollLeft;
                });

                scrollContainer.addEventListener('mouseleave', () => {
                    isDown = false;
                    scrollContainer.style.cursor = 'grab';
                });

                scrollContainer.addEventListener('mouseup', () => {
                    isDown = false;
                    scrollContainer.style.cursor = 'grab';
                });

                scrollContainer.addEventListener('mousemove', (e) => {
                    if (!isDown) return;
                    e.preventDefault();
                    const x = e.pageX - scrollContainer.offsetLeft;
                    const walk = (x - startX) * 2; // سرعة التمرير

                    // استخدام الدالة المساعدة لتحديد اتجاه التمرير المناسب
                    scrollContainer.scrollLeft = scrollLeft + getScrollDirection(-walk);

                    // تحديث حالة أزرار التمرير بعد كل حركة
                    updateScrollButtons();
                });
            }

            // تأثيرات الحركة عند التمرير
            const animateOnScroll = function() {
                const elements = document.querySelectorAll('.ad-item');

                elements.forEach(item => {
                    const position = item.getBoundingClientRect();

                    // إذا كان العنصر مرئيًا في نافذة العرض
                    if (position.top < window.innerHeight && position.bottom >= 0) {
                        if (!item.classList.contains('has-animated')) {
                            item.classList.add('has-animated');
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }
                    }
                });
            };

            // تطبيق التأثيرات عند التمرير
            window.addEventListener('scroll', animateOnScroll);

            // تطبيق التأثيرات عند تحميل الصفحة
            animateOnScroll();

            // تهيئة التصنيفات الفرعية
            const categoryItems = document.querySelectorAll('.category-item[data-category]');
            const subcategoryContainers = document.querySelectorAll('.subcategories-container');

            // إضافة مستمعي الأحداث للتصنيفات الرئيسية
            categoryItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    const categoryKey = this.getAttribute('data-category');

                    // إخفاء جميع حاويات التصنيفات الفرعية
                    subcategoryContainers.forEach(container => {
                        container.style.display = 'none';
                    });

                    // إظهار حاوية التصنيف الفرعي المناسبة
                    const subcategoryContainer = document.getElementById(`subcategories-${categoryKey}`);
                    if (subcategoryContainer) {
                        subcategoryContainer.style.display = 'block';
                    }
                });
            });

            // تهيئة شريط التمرير للتصنيفات الفرعية
            const subcategoriesScrolls = document.querySelectorAll('.subcategories-scroll');
            subcategoriesScrolls.forEach(scroll => {
                // تمرير التصنيفات الفرعية باستخدام الماوس (سحب وإفلات)
                let isDown = false;
                let startX;
                let scrollLeft;

                scroll.addEventListener('mousedown', (e) => {
                    isDown = true;
                    scroll.style.cursor = 'grabbing';
                    startX = e.pageX - scroll.offsetLeft;
                    scrollLeft = scroll.scrollLeft;
                });

                scroll.addEventListener('mouseleave', () => {
                    isDown = false;
                    scroll.style.cursor = 'grab';
                });

                scroll.addEventListener('mouseup', () => {
                    isDown = false;
                    scroll.style.cursor = 'grab';
                });

                scroll.addEventListener('mousemove', (e) => {
                    if (!isDown) return;
                    e.preventDefault();
                    const x = e.pageX - scroll.offsetLeft;
                    const walk = (x - startX) * 2; // سرعة التمرير

                    // استخدام الدالة المساعدة لتحديد اتجاه التمرير المناسب
                    scroll.scrollLeft = scrollLeft + getScrollDirection(-walk);
                });
            });

            // تأثير نبض للعناصر المميزة
            const featuredBadges = document.querySelectorAll('.ad-item .animate-pulse');
            featuredBadges.forEach(badge => {
                badge.style.animation = 'pulse 2s infinite';
            });

            // تأثيرات أزرار الترقيم
            const paginationButtons = document.querySelectorAll('.pagination-btn, .pagination-number');

            paginationButtons.forEach(button => {
                // تأثير الضغط على الزر
                button.addEventListener('mousedown', function() {
                    if (!this.classList.contains('disabled') && !this.classList.contains('active')) {
                        this.style.transform = 'scale(0.95)';
                    }
                });

                // إعادة الزر لحالته الأصلية
                button.addEventListener('mouseup', function() {
                    if (!this.classList.contains('disabled') && !this.classList.contains('active')) {
                        this.style.transform = '';
                    }
                });

                // إعادة الزر لحالته الأصلية عند إلغاء الضغط
                button.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('disabled') && !this.classList.contains('active')) {
                        this.style.transform = '';
                    }
                });

                // تأثير النقر
                button.addEventListener('click', function(e) {
                    if (!this.classList.contains('disabled') && !this.classList.contains('active')) {
                        // إنشاء تأثير تموج عند النقر
                        const ripple = document.createElement('span');
                        ripple.classList.add('pagination-ripple');

                        // تحديد موقع النقر
                        const rect = this.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;

                        // تعيين موقع التأثير
                        ripple.style.left = x + 'px';
                        ripple.style.top = y + 'px';

                        // إضافة التأثير للزر
                        this.appendChild(ripple);

                        // إزالة التأثير بعد انتهاء الحركة
                        setTimeout(() => {
                            ripple.remove();
                        }, 600);
                    }
                });
            });

            // إضافة نمط CSS لتأثير التموج
            const style = document.createElement('style');
            style.textContent = `
                .pagination-ripple {
                    position: absolute;
                    background: rgba(255, 255, 255, 0.7);
                    border-radius: 50%;
                    pointer-events: none;
                    transform: scale(0);
                    animation: pagination-ripple 0.6s linear;
                }

                @keyframes pagination-ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        });
    </script>

    <!-- Vue.js -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.2.36/dist/vue.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

    <!-- أنماط السلايدر للإعلانات الخارجية -->
    <style>
        .special-ads-container {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .special-ads-wrapper {
            position: relative;
            overflow: hidden;
            padding: 0 0 20px;
        }

        .slider-track {
            display: flex;
            transition: transform 0.5s ease-in-out;
            cursor: grab;
        }

        .slider-track:active {
            cursor: grabbing;
        }

        .slider-item {
            flex-shrink: 0;
            padding: 0 8px;
            transition: all 0.3s ease;
        }

        .slider-item.active {
            transform: scale(1.02);
        }

        .slider-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .slider-control-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f1f5f9;
            color: #0f172a;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .slider-control-btn:hover {
            background-color: #0066cc;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 102, 204, 0.2);
        }

        .slider-indicators {
            display: flex;
            justify-content: center;
            gap: 6px;
            margin-top: 12px;
        }

        .slider-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #cbd5e1;
            border: none;
            padding: 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .slider-indicator.active {
            background-color: #0066cc;
            width: 16px;
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .slider-item {
                width: 100%;
            }
        }
    </style>

    <!-- مكون الإعلانات الخاصة -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const { createApp, ref, onMounted, onUnmounted } = Vue;

            // مكون الإعلانات الخاصة
            const SpecialAds = {
                template: `
                    <div class="special-ads-container bg-white p-4 rounded-lg shadow-md mb-6" v-if="ads.length > 0">
                        <h3 class="text-lg font-bold mb-4 text-gray-800 flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-star text-yellow-500 mr-2"></i>
                                الإعلانات الخارجية
                            </div>
                            <div class="slider-controls flex items-center gap-2">
                                <span class="text-sm text-gray-500">@{{ currentIndex + 1 }}/@{{ ads.length }}</span>
                                <button @click="prevSlide" class="slider-control-btn bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-full w-8 h-8 flex items-center justify-center transition-colors">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                                <button @click="nextSlide" class="slider-control-btn bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-full w-8 h-8 flex items-center justify-center transition-colors">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                            </div>
                        </h3>
                        <div class="special-ads-wrapper relative overflow-hidden" ref="sliderContainer">
                            <div class="slider-track flex transition-transform duration-500 ease-in-out"
                                 :style="{ transform: \`translateX(\${translateX}%)\` }"
                                 @mousedown="startDrag"
                                 @touchstart="startDrag"
                                 @mouseup="endDrag"
                                 @touchend="endDrag"
                                 @mousemove="onDrag"
                                 @touchmove="onDrag"
                                 @mouseleave="endDrag">
                                <div v-for="(ad, index) in ads" :key="ad.id"
                                     class="slider-item flex-shrink-0 w-full md:w-1/3 px-2"
                                     :class="{ 'active': index === currentIndex }">
                                    <div class="special-ad-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-gray-200 h-full">
                                        <a @click.prevent="handleAdClick(ad)" :href="ad.url || '#'" class="block relative h-full">
                                            <div class="special-ad-image h-48 overflow-hidden relative">
                                                <img :src="getImageUrl(ad)" :alt="ad.title" class="w-full h-full object-cover transition-transform duration-300 hover:scale-105">
                                                <div class="absolute top-2 left-2 bg-red-600 text-white text-xs px-2 py-1 rounded-full shadow-md" v-if="ad.advertiser_name">
                                                    @{{ ad.advertiser_name }}
                                                </div>
                                                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent h-16 pointer-events-none"></div>
                                            </div>
                                            <div class="special-ad-title p-3 text-center">
                                                <h5 class="font-bold text-gray-800" v-text="ad.title"></h5>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- مؤشرات الشرائح -->
                            <div class="slider-indicators flex justify-center mt-4 gap-2">
                                <button v-for="(ad, index) in ads" :key="'ind-'+index"
                                        @click="goToSlide(index)"
                                        class="slider-indicator w-2 h-2 rounded-full transition-all duration-300"
                                        :class="index === currentIndex ? 'bg-blue-500 w-4' : 'bg-gray-300'">
                                </button>
                            </div>
                        </div>
                    </div>
                `,
                props: {
                    position: {
                        type: String,
                        required: true,
                        validator: value => ['top', 'middle', 'bottom', 'sidebar'].includes(value)
                    }
                },
                setup(props) {
                    const ads = ref([]);
                    const currentIndex = ref(0);
                    const translateX = ref(0);
                    const sliderContainer = ref(null);
                    const autoplayInterval = ref(null);
                    const isDragging = ref(false);
                    const startX = ref(0);
                    const currentTranslateX = ref(0);

                    const fetchAds = async () => {
                        try {
                            const response = await axios.get(`/api/special-ads/${props.position}`);
                            if (response.data.success) {
                                ads.value = response.data.data;
                                // بدء التشغيل التلقائي بعد تحميل الإعلانات
                                startAutoplay();
                            }
                        } catch (error) {
                            console.error('Error fetching special ads:', error);
                        }
                    };

                    const getImageUrl = (ad) => {
                        if (ad.image_id) {
                            return `/images/${ad.image_id}?v=${new Date().getTime()}`;
                        } else if (ad.image) {
                            return `/images/${ad.image.id}?v=${new Date().getTime()}`;
                        }
                        return 'https://via.placeholder.com/800x400?text=إعلان+مميز';
                    };

                    const handleAdClick = async (ad) => {
                        try {
                            const response = await axios.post(`/api/special-ads/click/${ad.id}`);
                            if (response.data.success && response.data.url) {
                                window.open(response.data.url, '_blank');
                            }
                        } catch (error) {
                            console.error('Error recording ad click:', error);
                            if (ad.url) {
                                window.open(ad.url, '_blank');
                            }
                        }
                    };

                    // وظائف السلايدر
                    const updateTranslateX = () => {
                        // حساب قيمة الإزاحة بناءً على الشاشة
                        const isMobile = window.innerWidth < 768;
                        const itemWidth = isMobile ? 100 : 33.33; // عرض العنصر بالنسبة المئوية
                        translateX.value = -currentIndex.value * itemWidth;
                    };

                    const nextSlide = () => {
                        if (ads.value.length <= 1) return;

                        currentIndex.value = (currentIndex.value + 1) % ads.value.length;
                        updateTranslateX();
                        resetAutoplay();
                    };

                    const prevSlide = () => {
                        if (ads.value.length <= 1) return;

                        currentIndex.value = (currentIndex.value - 1 + ads.value.length) % ads.value.length;
                        updateTranslateX();
                        resetAutoplay();
                    };

                    const goToSlide = (index) => {
                        currentIndex.value = index;
                        updateTranslateX();
                        resetAutoplay();
                    };

                    const startAutoplay = () => {
                        // إيقاف أي تشغيل تلقائي سابق
                        stopAutoplay();

                        // بدء تشغيل تلقائي جديد كل 5 ثواني
                        autoplayInterval.value = setInterval(() => {
                            nextSlide();
                        }, 5000);
                    };

                    const stopAutoplay = () => {
                        if (autoplayInterval.value) {
                            clearInterval(autoplayInterval.value);
                            autoplayInterval.value = null;
                        }
                    };

                    const resetAutoplay = () => {
                        stopAutoplay();
                        startAutoplay();
                    };

                    // وظائف السحب
                    const startDrag = (event) => {
                        isDragging.value = true;
                        startX.value = event.type === 'touchstart' ? event.touches[0].clientX : event.clientX;
                        currentTranslateX.value = translateX.value;

                        // إيقاف التشغيل التلقائي أثناء السحب
                        stopAutoplay();
                    };

                    const onDrag = (event) => {
                        if (!isDragging.value) return;

                        const currentX = event.type === 'touchmove' ? event.touches[0].clientX : event.clientX;
                        const diff = currentX - startX.value;

                        // حساب نسبة الإزاحة بناءً على عرض الشاشة
                        const isMobile = window.innerWidth < 768;
                        const itemWidth = isMobile ? 100 : 33.33; // عرض العنصر بالنسبة المئوية

                        // تحويل الفرق بالبكسل إلى نسبة مئوية
                        const containerWidth = sliderContainer.value ? sliderContainer.value.offsetWidth : 300;
                        const percentageDiff = (diff / containerWidth) * 100;

                        // تطبيق الإزاحة مع حدود للحركة
                        translateX.value = Math.max(
                            -itemWidth * (ads.value.length - 1),
                            Math.min(0, currentTranslateX.value + percentageDiff)
                        );
                    };

                    const endDrag = () => {
                        if (!isDragging.value) return;

                        isDragging.value = false;

                        // حساب الشريحة الأقرب بعد السحب
                        const isMobile = window.innerWidth < 768;
                        const itemWidth = isMobile ? 100 : 33.33;

                        // تحديد الشريحة الأقرب بناءً على الموضع الحالي
                        const nearestIndex = Math.round(Math.abs(translateX.value) / itemWidth);
                        currentIndex.value = Math.max(0, Math.min(ads.value.length - 1, nearestIndex));

                        // تحديث الموضع للانتقال بسلاسة إلى الشريحة الأقرب
                        updateTranslateX();

                        // إعادة تشغيل التشغيل التلقائي
                        startAutoplay();
                    };

                    // تحديث الإزاحة عند تغيير حجم النافذة
                    const handleResize = () => {
                        updateTranslateX();
                    };

                    // إعداد المكون
                    onMounted(() => {
                        window.addEventListener('resize', handleResize);
                        fetchAds();
                    });

                    // تنظيف عند إزالة المكون
                    onUnmounted(() => {
                        window.removeEventListener('resize', handleResize);
                        stopAutoplay();
                    });

                    return {
                        ads,
                        currentIndex,
                        translateX,
                        sliderContainer,
                        getImageUrl,
                        handleAdClick,
                        nextSlide,
                        prevSlide,
                        goToSlide,
                        startDrag,
                        onDrag,
                        endDrag
                    };
                }
            };

            // تثبيت المكون على كل عنصر يحتوي على الكلاس special-ads-app
            document.querySelectorAll('.special-ads-app').forEach(el => {
                const position = el.getAttribute('data-position');
                const app = createApp(SpecialAds, { position });
                app.mount(el);
            });
        });
    </script>
</body>
</html>