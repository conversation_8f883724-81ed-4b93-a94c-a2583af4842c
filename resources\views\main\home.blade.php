<!DOCTYPE html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="منصة إعلانات متنوعة تجمع بين البائعين والمشترين في مكان واحد" />
    <meta name="keywords" content="إعلانات, تسوق, بيع, شراء, وظائف, عقارات, سيارات, خدمات" />
    <title data-en="{{ site_name() }} - Your Marketplace for Everything" data-ar="{{ site_name() }} - {{ site_setting('site_slogan', 'سوقك لكل شيء') }}">{{ site_name() }} - {{ site_setting('site_slogan', 'سوقك لكل شيء') }}</title>
    <meta name="description" content="{{ site_description() }}">
    <meta name="keywords" content="{{ site_keywords() }}">
    <meta name="author" content="{{ site_setting('meta_author', site_name()) }}">
    <link rel="icon" type="image/x-icon" href="{{ site_favicon() }}" />
    <link rel="shortcut icon" type="image/x-icon" href="{{ site_favicon() }}" />
    <link rel="stylesheet" href="{{ asset('style.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="{{ asset('main.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <style>
      /* أسلوب التذييل */
      footer {
        background: linear-gradient(135deg, #2c3e50,rgba(223, 119, 33, 0.97));
        color: white;
        padding: 40px 20px;
        text-align: center;
        position: relative;
        overflow: hidden;
      }
      footer .footer-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 20px;
      }
      footer .footer-section {
        flex: 1;
        min-width: 200px;
      }
      footer h3 {
        font-size: 1.2rem;
        margin-bottom: 15px;
      }
      footer ul {
        list-style: none;
        padding: 0;
      }
      footer ul li {
        margin: 10px 0;
      }
      footer ul li a {
        color: #ecf0f1;
        text-decoration: none;
        transition: color 0.3s;
      }
      footer ul li a:hover {
        color: #f1c40f;
      }
      footer .footer-bottom {
        margin-top: 20px;
        font-size: 0.9rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        padding-top: 10px;
      }
      @media (max-width: 768px) {
        footer .footer-content {
          flex-direction: column;
          text-align: center;
        }
      }
    </style>
  </head>
  <body>
    <div id="particles-js"></div>
    <div class="loading-screen">
      <div class="ai-loader"></div>
    </div>

    <div class="language-selector">
      <button class="language-toggle">
        <span class="current-lang">EN</span>
        <span class="separator">/</span>
        <span class="other-lang">عربي</span>
      </button>
    </div>

    <!-- زر تحديث الموقع العائم -->
    @include('components.refresh-button', [
        'position' => 'fixed',
        'size' => 'normal',
        'style' => 'floating',
        'showText' => false
    ])

    <!-- زر الإعدادات العائم -->
    @include('components.settings-button', [
        'position' => 'fixed',
        'size' => 'normal',
        'style' => 'floating',
        'showText' => false
    ])

    <nav class="navbar">
      <div class="nav-brand" data-en="{{ site_name() }}" data-ar="{{ site_name() }}">
        <i class="fas fa-bullhorn animated-icon"></i>
        <span class="brand-text">{{ site_name() }}</span>
        <div class="brand-glow"></div>
      </div>
      <button class="hamburger" aria-label="Menu">
        <span></span>
        <span></span>
        <span></span>
      </button>
      <ul class="nav-links">
        <li><a href="#home" data-en="🏠 Home" data-ar="🏠 الرئيسية" class="nav-link-enhanced"><i class="fas fa-home"></i> <span>Home</span></a></li>
        <li><a href="#categories" data-en="📂 Categories" data-ar="📂 الفئات" class="nav-link-enhanced"><i class="fas fa-th-large"></i> <span>Categories</span></a></li>
        <li><a href="#about" data-en="ℹ️ About" data-ar="ℹ️ عن المنصة" class="nav-link-enhanced"><i class="fas fa-info-circle"></i> <span>About</span></a></li>
        <li><a href="#features" data-en="⭐ Features" data-ar="⭐ المميزات" class="nav-link-enhanced"><i class="fas fa-star"></i> <span>Features</span></a></li>
        <li><a href="#contact" data-en="📧 Contact" data-ar="📧 اتصل بنا" class="nav-link-enhanced"><i class="fas fa-envelope"></i> <span>Contact</span></a></li>
      </ul>
    </nav>



    <main>
      <section class="hero">
        <div class="hero-content">
          <h1 data-en="💼 Your Career & Marketplace Hub 🎯" data-ar="💼 مركزك المهني والتجاري الشامل 🎯">💼 Your Career & Marketplace Hub 🎯</h1>
          <p data-en="🌟 Build your career, find opportunities, trade with confidence, and connect with professionals in our comprehensive platform designed for ambitious individuals and businesses" data-ar="🌟 ابن مسيرتك المهنية، اعثر على الفرص، تاجر بثقة، وتواصل مع المحترفين في منصتنا الشاملة المصممة للأفراد والشركات الطموحة">🌟 Build your career, find opportunities, trade with confidence, and connect with professionals in our comprehensive platform designed for ambitious individuals and businesses</p>
          <div class="cta-buttons">
            <button class="cta-button primary" data-en="Browse Ads" onclick="window.location='{{ url('/ads') }}'" data-ar="تصفح الإعلانات"><i class="fas fa-search"></i> Browse Ads</button>
            <button class="cta-button primary" data-en="Post Ad" onclick="window.location='{{ url('/ads/create') }}'" data-ar="نشر إعلان"><i class="fas fa-plus-circle"></i> Post Ad</button>

            <button class="cta-button secondary" data-en="Real Estate" onclick="window.location='{{ url('/jobs') }}'" data-en="Jobs" onclick="window.location='{{ url('/jobs') }}'" data-ar="الوظائف"><i class="fas fa-briefcase"></i> Jobs</button>
          </div>
          <div class="auth-buttons">
            <button onclick="window.location='{{ url('/login') }}'" class="auth-button login" data-en="Login" data-ar="دخول"><i class="fas fa-sign-in-alt"></i> Login</button>
            <button onclick="window.location='{{ url('/register') }}'" class="auth-button register" data-en="Register" data-ar="تسجيل"><i class="fas fa-user-plus"></i> Register</button>
          </div>
        </div>

        <div class="hero-illustration">
          <div class="marketplace-visualization"></div>
        </div>
      </section>
    </main>

    <!-- قسم الفئات -->
    <section id="categories" class="categories-section py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div class="max-w-6xl mx-auto text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-8" data-en="Browse Categories" data-ar="تصفح الفئات">
          <i class="fas fa-th-large text-blue-600"></i> تصفح الفئات
        </h2>
        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-6">
          <a href="{{ url('/ads?search=&category=السيارات&location=') }}" class="category-card">
            <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center">
              <i class="fas fa-car text-4xl text-blue-600 mb-4"></i>
              <h3 class="text-xl font-semibold text-gray-800" data-en="Vehicles" data-ar="السيارات">السيارات</h3>
              <p class="text-sm text-gray-600 mt-2" data-en="Cars, motorcycles, boats & more" data-ar="سيارات، دراجات نارية، قوارب والمزيد">سيارات، دراجات نارية، قوارب والمزيد</p>
            </div>
          </a>
          <a href="{{ url('/ads?search=&category=الخدمات&location=') }}" class="category-card">
            <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center">
              <i class="fas fa-hands-helping text-4xl text-indigo-600 mb-4"></i>
              <h3 class="text-xl font-semibold text-gray-800" data-en="Services" data-ar="الخدمات">الخدمات</h3>
              <p class="text-sm text-gray-600 mt-2" data-en="Professional & personal services" data-ar="خدمات مهنية وشخصية">خدمات مهنية وشخصية</p>
            </div>
          </a>
          <a href="{{ url('/ads?search=&category=العقارات&location=') }}" class="category-card">
            <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center">
              <i class="fas fa-building text-4xl text-green-600 mb-4"></i>
              <h3 class="text-xl font-semibold text-gray-800" data-en="Real Estate" data-ar="العقارات">العقارات</h3>
              <p class="text-sm text-gray-600 mt-2" data-en="Houses, apartments, lands & more" data-ar="منازل، شقق، أراضي والمزيد">منازل، شقق، أراضي والمزيد</p>
            </div>
          </a>
          <a href="{{ url('/ads?search=&category=الإلكترونيات&location=') }}" class="category-card">
            <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center">
              <i class="fas fa-mobile-alt text-4xl text-red-600 mb-4"></i>
              <h3 class="text-xl font-semibold text-gray-800" data-en="Electronics" data-ar="الإلكترونيات">الإلكترونيات</h3>
              <p class="text-sm text-gray-600 mt-2" data-en="Phones, computers, gadgets & more" data-ar="هواتف، حواسيب، أجهزة والمزيد">هواتف، حواسيب، أجهزة والمزيد</p>
            </div>
          </a>
          <a href="{{ url('/ads?search=&category=أخرى&location=') }}" class="category-card">
            <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center">
              <i class="fas fa-ellipsis-h text-4xl text-purple-600 mb-4"></i>
              <h3 class="text-xl font-semibold text-gray-800" data-en="Other" data-ar="أخرى">أخرى</h3>
              <p class="text-sm text-gray-600 mt-2" data-en="Other categories and listings" data-ar="فئات وإعلانات أخرى">فئات وإعلانات أخرى</p>
            </div>
          </a>
          <a href="{{ url('/ads') }}" class="category-card">
            <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center">
              <i class="fas fa-th-large text-4xl text-gray-600 mb-4"></i>
              <h3 class="text-xl font-semibold text-gray-800" data-en="All Categories" data-ar="كل الفئات">كل الفئات</h3>
              <p class="text-sm text-gray-600 mt-2" data-en="Browse all listings" data-ar="تصفح جميع الإعلانات">تصفح جميع الإعلانات</p>
            </div>
          </a>
        </div>
      </div>
    </section>

    <!-- قسم الإعلانات المميزة -->
    <section id="featured" class="featured-section py-16 px-4 sm:px-6 lg:px-8 bg-white">
      <div class="max-w-6xl mx-auto">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center" data-en="Featured Listings" data-ar="إعلانات مميزة">
          <i class="fas fa-star text-yellow-500"></i> إعلانات مميزة
        </h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          <div class="listing-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div class="relative">
              <img src="https://static.hiamag.com/inline-images/%D9%85%D8%B3%D8%A7%D8%AD%D8%A9%20%D9%85%D8%B9%D9%8A%D8%B4%D8%A9%20%D8%AF%D8%A7%D9%81%D8%A6%D8%A9%20%D8%B6%D9%85%D9%86%20%D8%AF%D9%8A%D9%83%D9%88%D8%B1%20%D8%B4%D9%82%D8%A9%20%D9%81%D8%AE%D9%85%D8%A9.jpg" alt="Listing" class="w-full h-48 object-cover">
              <span class="absolute top-2 right-2 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded" data-en="FEATURED" data-ar="مميز">مميز</span>
            </div>
            <div class="p-4">
              <h3 class="text-xl font-semibold text-gray-800 mb-2" data-en="Luxury Apartment in Al-Olaya" data-ar="شقة فاخرة في حي العليا">شقة فاخرة في حي العليا</h3>
              <p class="text-gray-600 mb-2" data-en="4 bedrooms, 3 bathrooms, modern design" data-ar="4 غرف نوم، 3 حمامات، تصميم عصري">4 غرف نوم، 3 حمامات، تصميم عصري</p>
              <div class="flex justify-between items-center mt-4">
                <span class="text-blue-600 font-bold">8,500 ر.س/شهر</span>
                <span class="text-gray-500 text-sm"><i class="fas fa-map-marker-alt"></i> الرياض</span>
              </div>
            </div>
          </div>
          <div class="listing-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div class="relative">
              <img src="https://www.toyota.com.sa/-/media/feature/toyotaksa/vehicle/suv/lc300/new/features/exterior01.jpg?h=600&w=960&la=ar&hash=AF55B7E93543170CC881B0589118518E1184204D" alt="Listing" class="w-full h-48 object-cover">
              <span class="absolute top-2 right-2 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded" data-en="FEATURED" data-ar="مميز">مميز</span>
            </div>
            <div class="p-4">
              <h3 class="text-xl font-semibold text-gray-800 mb-2" data-en="2023 Toyota Land Cruiser GXR" data-ar="تويوتا لاندكروزر GXR موديل 2023">تويوتا لاندكروزر GXR موديل 2023</h3>
              <p class="text-gray-600 mb-2" data-en="15,000 km, full options, excellent condition" data-ar="15,000 كم، فل اوبشن، حالة الوكالة">15,000 كم، فل اوبشن، حالة الوكالة</p>
              <div class="flex justify-between items-center mt-4">
                <span class="text-blue-600 font-bold">380,000 ر.س</span>
                <span class="text-gray-500 text-sm"><i class="fas fa-map-marker-alt"></i> الرياض</span>
              </div>
            </div>
          </div>
          <div class="listing-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div class="relative">
              <img src="https://img.lovepik.com/bg/20240506/Exploring-No-Code-Programming-A-Vibrant-3D-Rendered-Concept-Background_8839035_wh1200.jpg" alt="Listing" class="w-full h-48 object-cover">
              <span class="absolute top-2 right-2 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded" data-en="FEATURED" data-ar="مميز">مميز</span>
            </div>
            <div class="p-4">
              <h3 class="text-xl font-semibold text-gray-800 mb-2" data-en="Software Engineer - Aramco" data-ar="مهندس برمجيات - أرامكو">مهندس برمجيات - أرامكو</h3>
              <p class="text-gray-600 mb-2" data-en="Full-time, 2+ years experience, Java/Python" data-ar="دوام كامل، خبرة 2+ سنوات، جافا/بايثون">دوام كامل، خبرة 2+ سنوات، جافا/بايثون</p>
              <div class="flex justify-between items-center mt-4">
                <span class="text-blue-600 font-bold">18,000-22,000 ر.س</span>
                <span class="text-gray-500 text-sm"><i class="fas fa-building"></i> الظهران</span>
              </div>
            </div>
          </div>
        </div>
        <div class="text-center mt-10">
          <a href="{{ url('/ads') }}" class="inline-block px-6 py-3 bg-blue-600 text-white font-semibold rounded-md hover:bg-blue-700 transition-colors duration-200" data-en="View All Listings" data-ar="عرض جميع الإعلانات">
            <i class="fas fa-list-ul mr-2"></i> عرض جميع الإعلانات
          </a>
        </div>
      </div>
    </section>

<!-- قسم "معلومات عنا" -->
<section id="about" class="about-section py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-gray-50 to-blue-50">
  <div class="max-w-4xl mx-auto text-center">
    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4" data-en="About Us" data-ar="معلومات عنا">
      <i class="fas fa-info-circle text-blue-600"></i> معلومات عنا
    </h2>
    <p class="text-lg text-gray-600 leading-relaxed mb-6" data-en="AdSouq is the leading Saudi marketplace connecting buyers and sellers across multiple categories. From real estate to vehicles, jobs to services, we provide a secure and user-friendly platform for all your needs in the Kingdom."
       data-ar="منصة إنشر هو السوق السعودي الرائد الذي يربط المشترين والبائعين عبر فئات متعددة. من العقارات إلى السيارات، ومن الوظائف إلى الخدمات، نقدم منصة آمنة وسهلة الاستخدام لجميع احتياجاتك في المملكة.">
      منصة إنشر هو السوق السعودي الرائد الذي يربط المشترين والبائعين عبر فئات متعددة. من العقارات إلى السيارات، ومن الوظائف إلى الخدمات، نقدم منصة آمنة وسهلة الاستخدام لجميع احتياجاتك في المملكة.
    </p>
    <div class="flex flex-wrap justify-center gap-8 mt-8">
      <div class="flex items-center">
        <div class="bg-blue-100 p-3 rounded-full mr-4">
          <i class="fas fa-users text-2xl text-blue-600"></i>
        </div>
        <div class="text-left">
          <h3 class="font-bold text-gray-800" data-en="2M+ Users" data-ar="+2 مليون مستخدم">+2 مليون مستخدم</h3>
          <p class="text-sm text-gray-600" data-en="Active Saudi community" data-ar="مجتمع سعودي نشط">مجتمع سعودي نشط</p>
        </div>
      </div>
      <div class="flex items-center">
        <div class="bg-green-100 p-3 rounded-full mr-4">
          <i class="fas fa-tag text-2xl text-green-600"></i>
        </div>
        <div class="text-left">
          <h3 class="font-bold text-gray-800" data-en="1M+ Listings" data-ar="+1 مليون إعلان">+1 مليون إعلان</h3>
          <p class="text-sm text-gray-600" data-en="Across all categories" data-ar="عبر جميع الفئات">عبر جميع الفئات</p>
        </div>
      </div>
      <div class="flex items-center">
        <div class="bg-purple-100 p-3 rounded-full mr-4">
          <i class="fas fa-map-marked-alt text-2xl text-purple-600"></i>
        </div>
        <div class="text-left">
          <h3 class="font-bold text-gray-800" data-en="13 Regions" data-ar="13 منطقة">13 منطقة</h3>
          <p class="text-sm text-gray-600" data-en="Coverage across KSA" data-ar="تغطية في جميع مناطق المملكة">تغطية في جميع مناطق المملكة</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- قسم "المميزات" -->
<section id="features" class="features-section py-16 px-4 sm:px-6 lg:px-8 bg-white">
  <div class="max-w-6xl mx-auto text-center">
    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-12" data-en="Our Features" data-ar="مميزاتنا">
      <i class="fas fa-star text-yellow-500"></i> Our Features
    </h2>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="feature-card bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 border-t-4 border-blue-500">
        <div class="text-center mb-4">
          <i class="fas fa-shield-alt text-4xl text-blue-500"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-3" data-en="Secure Transactions" data-ar="معاملات آمنة">
          Secure Transactions
        </h3>
        <p class="text-gray-600" data-en="Protected payment system and verified user profiles for safe trading"
           data-ar="نظام دفع محمي وملفات مستخدمين موثقة للتداول الآمن">
          Protected payment system and verified user profiles for safe trading
        </p>
      </div>
      <div class="feature-card bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 border-t-4 border-green-500">
        <div class="text-center mb-4">
          <i class="fas fa-search text-4xl text-green-500"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-3" data-en="Smart Search" data-ar="بحث ذكي">
          Smart Search
        </h3>
        <p class="text-gray-600" data-en="Advanced filters and AI-powered recommendations to find exactly what you need"
           data-ar="مرشحات متقدمة وتوصيات مدعومة بالذكاء الاصطناعي للعثور على ما تحتاجه بالضبط">
          Advanced filters and AI-powered recommendations to find exactly what you need
        </p>
      </div>
      <div class="feature-card bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 border-t-4 border-purple-500">
        <div class="text-center mb-4">
          <i class="fas fa-bell text-4xl text-purple-500"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-3" data-en="Real-time Notifications" data-ar="إشعارات فورية">
          Real-time Notifications
        </h3>
        <p class="text-gray-600" data-en="Instant alerts for new listings, messages, and offers that match your interests"
           data-ar="تنبيهات فورية للإعلانات الجديدة والرسائل والعروض التي تتناسب مع اهتماماتك">
          Instant alerts for new listings, messages, and offers that match your interests
        </p>
      </div>
      <div class="feature-card bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 border-t-4 border-red-500">
        <div class="text-center mb-4">
          <i class="fas fa-comments text-4xl text-red-500"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-3" data-en="In-app Messaging" data-ar="مراسلة داخل التطبيق">
          In-app Messaging
        </h3>
        <p class="text-gray-600" data-en="Secure communication channel between buyers and sellers with translation support"
           data-ar="قناة اتصال آمنة بين المشترين والبائعين مع دعم الترجمة">
          Secure communication channel between buyers and sellers with translation support
        </p>
      </div>
    </div>
  </div>
</section>

<!-- قسم "اتصل بنا" -->
<section id="contact" class="contact-section py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-50 to-gray-50">
  <div class="max-w-4xl mx-auto">
    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center" data-en="Contact Us" data-ar="اتصل بنا">
      <i class="fas fa-envelope text-blue-600"></i> Contact Us
    </h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
      <div class="contact-info">
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-xl font-semibold text-gray-800 mb-6" data-en="Get In Touch" data-ar="تواصل معنا">
            <i class="fas fa-headset text-blue-600 mr-2"></i> Get In Touch
          </h3>
          <div class="space-y-4">
            <div class="flex items-start">
              <div class="bg-blue-100 p-3 rounded-full mr-4">
                <i class="fas fa-map-marker-alt text-blue-600"></i>
              </div>
              <div>
                <h4 class="font-semibold text-gray-800" data-en="Our Location" data-ar="موقعنا">Our Location</h4>
                <p class="text-gray-600" data-en="{{ site_setting('site_address', 'Riyadh, Kingdom of Saudi Arabia') }}" data-ar="{{ site_setting('site_address', 'الرياض، المملكة العربية السعودية') }}">{{ site_setting('site_address', 'الرياض، المملكة العربية السعودية') }}</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="bg-green-100 p-3 rounded-full mr-4">
                <i class="fas fa-phone-alt text-green-600"></i>
              </div>
              <div>
                <h4 class="font-semibold text-gray-800" data-en="Phone Number" data-ar="رقم الهاتف">Phone Number</h4>
                <p class="text-gray-600" dir="ltr">{{ contact_phone() }}</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="bg-purple-100 p-3 rounded-full mr-4">
                <i class="fas fa-envelope text-purple-600"></i>
              </div>
              <div>
                <h4 class="font-semibold text-gray-800" data-en="Email Address" data-ar="البريد الإلكتروني">Email Address</h4>
                <p class="text-gray-600">{{ contact_email() }}</p>
              </div>
            </div>
          </div>
          <div class="mt-8">
            <h4 class="font-semibold text-gray-800 mb-4" data-en="Follow Us" data-ar="تابعنا">Follow Us</h4>
            <div class="flex space-x-4">
              <a href="#" class="bg-blue-600 text-white p-3 rounded-full hover:bg-blue-700 transition-colors duration-200">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="bg-blue-400 text-white p-3 rounded-full hover:bg-blue-500 transition-colors duration-200">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="bg-pink-600 text-white p-3 rounded-full hover:bg-pink-700 transition-colors duration-200">
                <i class="fab fa-instagram"></i>
              </a>
              <a href="#" class="bg-blue-700 text-white p-3 rounded-full hover:bg-blue-800 transition-colors duration-200">
                <i class="fab fa-linkedin-in"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
      <div class="contact-form-container">
        <form class="contact-form bg-white p-6 rounded-lg shadow-md space-y-6">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1" data-en="Your Name" data-ar="الاسم">Your Name</label>
            <input type="text" id="name" placeholder="John Doe" data-en-placeholder="John Doe" data-ar-placeholder="محمد علي"
                   class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
          </div>
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1" data-en="Email Address" data-ar="البريد الإلكتروني">Email Address</label>
            <input type="email" id="email" placeholder="<EMAIL>" data-en-placeholder="<EMAIL>" data-ar-placeholder="بريدك@مثال.كوم"
                   class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
          </div>
          <div>
            <label for="subject" class="block text-sm font-medium text-gray-700 mb-1" data-en="Subject" data-ar="الموضوع">Subject</label>
            <input type="text" id="subject" placeholder="How can we help?" data-en-placeholder="How can we help?" data-ar-placeholder="كيف يمكننا مساعدتك؟"
                   class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
          </div>
          <div>
            <label for="message" class="block text-sm font-medium text-gray-700 mb-1" data-en="Message" data-ar="الرسالة">Message</label>
            <textarea id="message" placeholder="Your message here..." data-en-placeholder="Your message here..." data-ar-placeholder="رسالتك هنا..."
                      class="w-full p-3 border border-gray-300 rounded-md h-32 resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"></textarea>
          </div>
          <button type="submit" data-en="Send Message" data-ar="إرسال الرسالة"
                  class="w-full px-6 py-3 bg-blue-600 text-white font-semibold rounded-md hover:bg-blue-700 transition-colors duration-200">
            <i class="fas fa-paper-plane mr-2"></i> Send Message
          </button>
        </form>
      </div>
    </div>
  </div>
</section>

<!-- قسم الشهادات -->
<section class="testimonials py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
  <div class="max-w-6xl mx-auto">
    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-12 text-center" data-en="Customer Reviews" data-ar="تقييمات العملاء">
      <i class="fas fa-quote-left text-yellow-500"></i> Customer Reviews
    </h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="testimonial-card bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center mb-4">
          <div class="testimonial-avatar bg-blue-100 p-2 rounded-full mr-4">
            <i class="fas fa-user text-2xl text-blue-600"></i>
          </div>
          <div>
            <h4 class="font-bold text-gray-800" data-en="Mohammed Al-Qahtani" data-ar="محمد القحطاني">Mohammed Al-Qahtani</h4>
            <div class="text-yellow-500 flex">
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
            </div>
          </div>
        </div>
        <p class="text-gray-600 italic" data-en="Found my dream apartment in Al-Olaya district through AdSouq. The listing details were accurate and the transaction was smooth. Highly recommended!"
           data-ar="وجدت شقة أحلامي في حي العليا من خلال منصة إنشر. كانت تفاصيل الإعلان دقيقة والمعاملة سلسة. أنصح به بشدة!">
          وجدت شقة أحلامي في حي العليا من خلال منصة إنشر. كانت تفاصيل الإعلان دقيقة والمعاملة سلسة. أنصح به بشدة!
        </p>
        <p class="text-gray-500 text-sm mt-4" data-en="Real Estate Buyer - Riyadh" data-ar="مشتري عقار - الرياض">مشتري عقار - الرياض</p>
      </div>
      <div class="testimonial-card bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center mb-4">
          <div class="testimonial-avatar bg-green-100 p-2 rounded-full mr-4">
            <i class="fas fa-user text-2xl text-green-600"></i>
          </div>
          <div>
            <h4 class="font-bold text-gray-800" data-en="Noura Al-Subaie" data-ar="نورة السبيعي">نورة السبيعي</h4>
            <div class="text-yellow-500 flex">
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star-half-alt"></i>
            </div>
          </div>
        </div>
        <p class="text-gray-600 italic" data-en="As a seller, I've tried Haraj and other platforms but AdSouq gives me the best exposure. Sold my Toyota Camry within just 3 days!"
           data-ar="كبائعة، جربت حراج ومنصات أخرى لكن منصة إنشر يمنحني أفضل ظهور. بعت سيارتي تويوتا كامري خلال 3 أيام فقط!">
          كبائعة، جربت حراج ومنصات أخرى لكن منصة إنشر يمنحني أفضل ظهور. بعت سيارتي تويوتا كامري خلال 3 أيام فقط!
        </p>
        <p class="text-gray-500 text-sm mt-4" data-en="Vehicle Seller - Jeddah" data-ar="بائعة سيارة - جدة">بائعة سيارة - جدة</p>
      </div>
      <div class="testimonial-card bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center mb-4">
          <div class="testimonial-avatar bg-purple-100 p-2 rounded-full mr-4">
            <i class="fas fa-user text-2xl text-purple-600"></i>
          </div>
          <div>
            <h4 class="font-bold text-gray-800" data-en="Khalid Al-Otaibi" data-ar="خالد العتيبي">خالد العتيبي</h4>
            <div class="text-yellow-500 flex">
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
            </div>
          </div>
        </div>
        <p class="text-gray-600 italic" data-en="Found my current position at Aramco through AdSouq. The application process was straightforward and the HR team responded quickly. Much better than LinkedIn and Bayt!"
           data-ar="وجدت وظيفتي الحالية في أرامكو من خلال منصة إنشر. كانت عملية التقديم مباشرة واستجاب فريق الموارد البشرية بسرعة. أفضل بكثير من لينكد إن وبيت!">
          وجدت وظيفتي الحالية في أرامكو من خلال منصة إنشر. كانت عملية التقديم مباشرة واستجاب فريق الموارد البشرية بسرعة. أفضل بكثير من لينكد إن وبيت!
        </p>
        <p class="text-gray-500 text-sm mt-4" data-en="Job Seeker - Dammam" data-ar="باحث عن عمل - الدمام">باحث عن عمل - الدمام</p>
      </div>
    </div>
  </div>
</section>

    <style>
    /* Additional Styles for the new marketplace design */
    :root {
      --primary-color: {{ primary_color() }};
      --primary-dark: {{ primary_color() }};
      --secondary-color: {{ secondary_color() }};
      --accent-color: {{ primary_color() }};
      --danger-color: #ef4444; /* Red 500 */
      --dark-color: #1f2937; /* Gray 800 */
      --light-color: #f9fafb; /* Gray 50 */
      --text-color: #374151; /* Gray 700 */
      --text-light: #6b7280; /* Gray 500 */
      --border-color: #e5e7eb; /* Gray 200 */
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    /* Global Styles */
    body {
      color: var(--text-color);
      font-family: 'Inter', system-ui, -apple-system, sans-serif;
    }

    /* Navbar Enhancements */
    .navbar {
      background: rgba(255, 255, 255, 0.95);
      box-shadow: var(--shadow);
    }

    .nav-brand {
      font-size: 1.75rem;
      font-weight: 700;
      background: linear-gradient(135deg, #3AB0FF, #1D8DF0, #E67E22);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      position: relative;
      padding: 0.5rem;
      border-radius: 10px;
      transition: all 0.3s ease;
    }

    .nav-brand:hover {
      transform: scale(1.05);
      filter: drop-shadow(0 0 10px rgba(58, 176, 255, 0.5));
    }

    .animated-icon {
      animation: iconPulse 2s ease-in-out infinite;
    }

    @keyframes iconPulse {
      0%, 100% {
        transform: scale(1);
        color: #3AB0FF;
      }
      50% {
        transform: scale(1.1);
        color: #E67E22;
      }
    }

    .brand-glow {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(58, 176, 255, 0.1), rgba(230, 126, 34, 0.1));
      border-radius: 10px;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;
    }

    .nav-brand:hover .brand-glow {
      opacity: 1;
    }

    .nav-links a {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      padding: 0.5rem 1rem;
      border-radius: 25px;
      position: relative;
      overflow: hidden;
    }

    .nav-link-enhanced {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(58, 176, 255, 0.2);
    }

    .nav-link-enhanced::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(58, 176, 255, 0.3), transparent);
      transition: left 0.5s;
    }

    .nav-link-enhanced:hover::before {
      left: 100%;
    }

    .nav-links a:hover {
      color: #3AB0FF;
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(58, 176, 255, 0.3);
      background: rgba(58, 176, 255, 0.1);
    }

    .nav-links a i {
      transition: transform 0.3s ease;
    }

    .nav-links a:hover i {
      transform: scale(1.2) rotate(5deg);
    }

    /* Hero Section Enhancements */
    .hero {
      background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
      position: relative;
      overflow: hidden;
    }

    .hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%233b82f6" fill-opacity="0.05" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,186.7C1248,149,1344,107,1392,85.3L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E');
      background-position: bottom;
      background-repeat: no-repeat;
      background-size: cover;
      z-index: 0;
    }

    .hero-content {
      position: relative;
      z-index: 2;
    }

    .hero h1 {
      font-size: 3.5rem;
      font-weight: 800;
      line-height: 1.2;
      margin-bottom: 1.5rem;
      background: linear-gradient(135deg, #2E86AB, #A23B72, #F18F01);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      /* إزالة التوهج والظلال */
    }

    /* Button Styles */
    .cta-button {
      padding: 0.875rem 1.75rem;
      border-radius: 50px;
      font-weight: 600;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      box-shadow: var(--shadow);
    }

    .cta-button:hover {
      transform: translateY(-3px);
      box-shadow: var(--shadow-lg);
    }

    .cta-button.primary {
      background: linear-gradient(135deg, #3AB0FF, #1D8DF0);
      color: white;
      position: relative;
      overflow: hidden;
    }

    .cta-button.primary::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s;
    }

    .cta-button.primary:hover::before {
      left: 100%;
    }

    .cta-button.secondary {
      background: white;
      color: var(--primary-color);
      border: 2px solid var(--primary-color);
      position: relative;
      overflow: hidden;
    }

    .cta-button.secondary::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 100%;
      background: linear-gradient(135deg, #3AB0FF, #1D8DF0);
      transition: width 0.3s ease;
      z-index: -1;
    }

    .cta-button.secondary:hover::before {
      width: 100%;
    }

    .cta-button.secondary:hover {
      color: white;
      border-color: #1D8DF0;
    }

    .auth-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .auth-button {
      padding: 0.75rem 1.5rem;
      border-radius: 50px;
      font-weight: 600;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
    }

    .auth-button.login {
      background: transparent;
      color: var(--primary-color);
      border: 1px solid var(--primary-color);
    }

    .auth-button.register {
      background: var(--primary-color);
      color: white;
    }

    .auth-button:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow);
    }

    /* Categories Section */
    .category-card {
      text-decoration: none;
      color: inherit;
      transition: all 0.3s ease;
    }

    .category-card:hover {
      transform: translateY(-5px);
    }

    /* Featured Listings */
    .listing-card {
      transition: all 0.3s ease;
    }

    .listing-card:hover {
      transform: translateY(-5px);
    }

    /* Testimonials */
    .testimonials {
      position: relative;
      overflow: hidden;
    }

    .testimonials::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%233b82f6" fill-opacity="0.05" d="M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,213.3C672,192,768,128,864,128C960,128,1056,192,1152,213.3C1248,235,1344,213,1392,202.7L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E');
      background-position: bottom;
      background-repeat: no-repeat;
      background-size: cover;
      z-index: 0;
      opacity: 0.5;
    }

    /* Marketplace Visualization */
    .marketplace-visualization {
      width: 100%;
      height: 100%;
      background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"%3E%3Crect fill="%233b82f6" width="100" height="100" transform="translate(50 50)"%3E%3C/rect%3E%3C/svg%3E');
      background-size: cover;
      position: relative;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
      .hero h1 {
        font-size: 2.5rem;
      }

      .cta-buttons, .auth-buttons {
        flex-direction: column;
        width: 100%;
      }

      .cta-button, .auth-button {
        width: 100%;
      }
    }













    :root {
  --primary-color: {{ primary_color() }};
  --secondary-color: {{ secondary_color() }};
  --background-color: #f8f9fa;
  --text-color: #2c3e50;
  --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}












* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
  overflow-x: hidden;
  transition: all 0.3s ease;
}

body[dir="rtl"] {
  font-family: 'Cairo', 'Inter', system-ui, -apple-system, sans-serif;
}

/* Language Selector - Moved to bottom */
.language-selector {
  position: fixed;
  bottom: 2rem;
  right: 1rem;
  z-index: 1000;
}

body[dir="rtl"] .language-selector {
  right: auto;
  left: 1rem;
}

.language-toggle {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--primary-color);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.language-toggle:hover {
  background: var(--primary-color);
  color: white;
}

.separator {
  margin: 0 0.3rem;
  opacity: 0.5;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: opacity 0.5s;
}

.ai-loader {
  width: 60px;
  height: 60px;
  border: 4px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

/* Navbar */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

body[dir="rtl"] .navbar {
  flex-direction: row-reverse;
}

.nav-brand {
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-links {
  display: flex;
  gap: 2rem;
  list-style: none;
}

body[dir="rtl"] .nav-links {
  flex-direction: row-reverse;
}

.nav-links a {
  color: var(--text-color);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-links a:hover {
  color: var(--primary-color);
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 6rem 2rem 2rem;
  position: relative;
  overflow: hidden;
}

body[dir="rtl"] .hero {
  text-align: right;
}

.hero-content {
  max-width: 600px;
  z-index: 2;
}

.hero h1 {
  font-size: 3.5rem;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hero p {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  color: #2c3e50;
  font-weight: 500;
  line-height: 1.6;
  /* خلفية شفافة بدون تأثيرات */
  background: transparent;
  padding: 1rem 1.5rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
}

body[dir="rtl"] .cta-buttons {
  flex-direction: row-reverse;
}

.cta-button {
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.cta-button.employer {
  background: var(--gradient);
  color: white;
}

.cta-button.jobseeker {
  background: white;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Particles Background - Career Themed */
#particles-js {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  background: linear-gradient(135deg,
    rgba(46, 134, 171, 0.03) 0%,
    rgba(162, 59, 114, 0.02) 35%,
    rgba(241, 143, 1, 0.02) 70%,
    rgba(199, 62, 29, 0.03) 100%);
  backdrop-filter: blur(0.3px);
}

/* Career-themed particle effects */
#particles-js::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(46, 134, 171, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(162, 59, 114, 0.04) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(241, 143, 1, 0.03) 0%, transparent 50%);
  z-index: -1;
  pointer-events: none;
}

/* تحسين تأثير الجسيمات على الشاشات الصغيرة */
@media (max-width: 768px) {
  #particles-js {
    opacity: 0.7;
  }
}

/* تأثير إضافي للخلفية */
.hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 70%, rgba(58, 176, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(230, 126, 34, 0.08) 0%, transparent 50%);
  z-index: 1;
  pointer-events: none;
}


/* Hamburger Menu */
.hamburger {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}



.hamburger span {
  display: block;
  width: 25px;
  height: 3px;
  background: var(--text-color);
  margin: 5px 0;
  transition: 0.3s;
}

/* Language Switch Transition */
[data-en], [data-ar] {
  transition: opacity 0.3s ease;
}

/* Animations */
@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hamburger {
    display: block;
    z-index: 101;
  }

  .nav-links {
    position: fixed;
    top: 0;
    right: -100%;
    height: 100vh;
    width: 70%;
    background: white;
    flex-direction: column;
    padding: 6rem 2rem;
    transition: 0.3s;
  }

  body[dir="rtl"] .nav-links {
    right: auto;

    left: -100%;
     position: fixed;
    top: 0;

    height: 100vh;
    width: 70%;
    background: white;
    flex-direction: column;
    padding: 6rem 2rem;
    transition: 0.3s;
  }

  .nav-links.active {
    right: 0;
  }

  body[dir="rtl"] .nav-links.active {
    left: 0;
    right: auto;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .cta-buttons {
    flex-direction: column;
  }

  body[dir="rtl"] .cta-buttons {
    flex-direction: column;
  }
}

/* Font Loading for Arabic */
@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
  font-display: swap;
}


    </style>
    <!-- التذييل الجديد -->
    <footer class="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-16 px-4 sm:px-6 lg:px-8">
      <div class="max-w-6xl mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-12">
          <div class="footer-section col-span-1 md:col-span-1">
            <div class="flex items-center gap-2 mb-4">
              <i class="fas fa-bullhorn text-2xl text-yellow-400"></i>
              <h3 class="text-xl font-bold" data-en="AdSouq" data-ar="منصة إنشر">AdSouq</h3>
            </div>
            <p class="text-blue-100 mb-6" data-en="Your one-stop marketplace for all your buying and selling needs" data-ar="سوقك الشامل لجميع احتياجات البيع والشراء والوظائف">Your one-stop marketplace for all your buying and selling needs</p>
            <div class="flex space-x-4">
              <a href="#" class="bg-blue-800 hover:bg-blue-600 text-white p-2 rounded-full transition-colors duration-200">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="bg-blue-800 hover:bg-blue-600 text-white p-2 rounded-full transition-colors duration-200">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="bg-blue-800 hover:bg-blue-600 text-white p-2 rounded-full transition-colors duration-200">
                <i class="fab fa-instagram"></i>
              </a>
              <a href="#" class="bg-blue-800 hover:bg-blue-600 text-white p-2 rounded-full transition-colors duration-200">
                <i class="fab fa-linkedin-in"></i>
              </a>
            </div>
          </div>
          <div class="footer-section">
            <h3 class="text-lg font-semibold mb-4" data-en="Categories" data-ar="الفئات"><i class="fas fa-th-large text-yellow-400 mr-2"></i> Categories</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-blue-100 hover:text-white transition-colors duration-200 flex items-center gap-2"><i class="fas fa-angle-right text-xs"></i> <span data-en="Vehicles" data-ar="سيارات">Vehicles</span></a></li>
              <li><a href="#" class="text-blue-100 hover:text-white transition-colors duration-200 flex items-center gap-2"><i class="fas fa-angle-right text-xs"></i> <span data-en="Real Estate" data-ar="عقارات">Real Estate</span></a></li>
              <li><a href="#" class="text-blue-100 hover:text-white transition-colors duration-200 flex items-center gap-2"><i class="fas fa-angle-right text-xs"></i> <span data-en="Jobs" data-ar="وظائف">Jobs</span></a></li>
              <li><a href="#" class="text-blue-100 hover:text-white transition-colors duration-200 flex items-center gap-2"><i class="fas fa-angle-right text-xs"></i> <span data-en="Electronics" data-ar="إلكترونيات">Electronics</span></a></li>
              <li><a href="#" class="text-blue-100 hover:text-white transition-colors duration-200 flex items-center gap-2"><i class="fas fa-angle-right text-xs"></i> <span data-en="Services" data-ar="خدمات">Services</span></a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h3 class="text-lg font-semibold mb-4" data-en="Quick Links" data-ar="روابط سريعة"><i class="fas fa-link text-yellow-400 mr-2"></i> Quick Links</h3>
            <ul class="space-y-2">
              <li><a href="#home" class="text-blue-100 hover:text-white transition-colors duration-200 flex items-center gap-2"><i class="fas fa-angle-right text-xs"></i> <span data-en="Home" data-ar="الرئيسية">Home</span></a></li>
              <li><a href="#about" class="text-blue-100 hover:text-white transition-colors duration-200 flex items-center gap-2"><i class="fas fa-angle-right text-xs"></i> <span data-en="About" data-ar="عن المنصة">About</span></a></li>
              <li><a href="#features" class="text-blue-100 hover:text-white transition-colors duration-200 flex items-center gap-2"><i class="fas fa-angle-right text-xs"></i> <span data-en="Features" data-ar="المميزات">Features</span></a></li>
              <li><a href="#contact" class="text-blue-100 hover:text-white transition-colors duration-200 flex items-center gap-2"><i class="fas fa-angle-right text-xs"></i> <span data-en="Contact" data-ar="اتصل بنا">Contact</span></a></li>
              <li><a href="#" class="text-blue-100 hover:text-white transition-colors duration-200 flex items-center gap-2"><i class="fas fa-angle-right text-xs"></i> <span data-en="Privacy Policy" data-ar="سياسة الخصوصية">Privacy Policy</span></a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h3 class="text-lg font-semibold mb-4" data-en="Contact Us" data-ar="اتصل بنا"><i class="fas fa-envelope text-yellow-400 mr-2"></i> Contact Us</h3>
            <ul class="space-y-3">
              <li class="flex items-start gap-3">
                <i class="fas fa-map-marker-alt mt-1 text-yellow-400"></i>
                <span data-en="Riyadh, Kingdom of Saudi Arabia" data-ar="الرياض، المملكة العربية السعودية">الرياض، المملكة العربية السعودية</span>
              </li>
              <li class="flex items-start gap-3">
                <i class="fas fa-phone-alt mt-1 text-yellow-400"></i>
                <span dir="ltr">+966 51 159 1846</span>
              </li>
              <li class="flex items-start gap-3">
                <i class="fas fa-envelope mt-1 text-yellow-400"></i>
                <span><EMAIL></span>
              </li>
              <li class="flex items-start gap-3">
                <i class="fas fa-clock mt-1 text-yellow-400"></i>
                <span data-en="Sun - Thu: 9:00 AM - 6:00 PM" data-ar="الأحد - الخميس: 9:00 ص - 6:00 م">الأحد - الخميس: 9:00 ص - 6:00 م</span>
              </li>
            </ul>
          </div>
        </div>
        <div class="border-t border-blue-600 mt-12 pt-8 text-center">
          <p class="text-blue-200" data-en="&copy; 2023 AdSouq. All rights reserved." data-ar="&copy; 2023 منصة إنشر. جميع الحقوق محفوظة.">&copy; 2023 منصة إنشر. جميع الحقوق محفوظة.</p>
          <div class="mt-4">
            <a href="#" class="text-blue-200 hover:text-white mx-2 transition-colors duration-200" data-en="Terms of Service" data-ar="شروط الخدمة">Terms of Service</a>
            <span class="text-blue-500">|</span>
            <a href="#" class="text-blue-200 hover:text-white mx-2 transition-colors duration-200" data-en="Privacy Policy" data-ar="سياسة الخصوصية">Privacy Policy</a>
            <span class="text-blue-500">|</span>
            <a href="#" class="text-blue-200 hover:text-white mx-2 transition-colors duration-200" data-en="Sitemap" data-ar="خريطة الموقع">Sitemap</a>
          </div>
        </div>
      </div>
    </footer>

    <script type="module" src="main.js"></script>
  </body>
</html>