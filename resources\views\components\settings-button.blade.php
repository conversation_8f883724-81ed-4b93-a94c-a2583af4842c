@props([
    'position' => 'fixed', // fixed, inline
    'size' => 'normal', // small, normal, large
    'style' => 'floating', // floating, button, icon, dropdown
    'showText' => true,
    'user' => null
])

@php
    $currentUser = $user ?? auth()->user();
    $sizeClasses = [
        'small' => 'w-10 h-10 text-sm',
        'normal' => 'w-12 h-12 text-base',
        'large' => 'w-16 h-16 text-lg'
    ];
    $buttonSize = $sizeClasses[$size] ?? $sizeClasses['normal'];
@endphp

<div class="settings-button-container">
    <style>
        .settings-button-container {
            position: relative;
            z-index: 1000;
        }
        
        .settings-button-fixed {
            position: fixed;
            top: 80px;
            left: 20px;
            z-index: 1000;
        }
        
        .settings-button {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .settings-button:hover {
            background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
        
        .settings-button:active {
            transform: translateY(0) scale(0.95);
        }
        
        .settings-icon {
            transition: transform 0.3s ease;
            font-size: 1.2rem;
        }
        
        .settings-button:hover .settings-icon {
            transform: rotate(90deg);
        }
        
        .settings-button-inline {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            border-radius: 50rem;
            padding: 0.5rem 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
        }
        
        .settings-button-inline:hover {
            background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
        }
        
        .settings-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            padding: 1rem;
            min-width: 280px;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .settings-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .settings-option {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 0.5rem;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
            border: 1px solid transparent;
            cursor: pointer;
        }
        
        .settings-option:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            text-decoration: none;
            color: #333;
            transform: translateX(-5px);
            border-color: #dee2e6;
        }
        
        .settings-option.profile:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: #2196f3;
        }
        
        .settings-option.privacy:hover {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-color: #4caf50;
        }
        
        .settings-option.notifications:hover {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
            border-color: #ff9800;
        }
        
        .settings-option.theme:hover {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border-color: #9c27b0;
        }
        
        .settings-option.logout:hover {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-color: #f44336;
            color: #d32f2f;
        }
        
        .settings-option-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.75rem;
            font-size: 1.1rem;
        }
        
        .profile .settings-option-icon { background: #2196f3; color: white; }
        .privacy .settings-option-icon { background: #4caf50; color: white; }
        .notifications .settings-option-icon { background: #ff9800; color: white; }
        .theme .settings-option-icon { background: #9c27b0; color: white; }
        .logout .settings-option-icon { background: #f44336; color: white; }
        
        .settings-option-text {
            flex: 1;
        }
        
        .settings-option-label {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .settings-option-description {
            font-size: 0.875rem;
            color: #6c757d;
            margin: 0;
        }
        
        .settings-tooltip {
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            pointer-events: none;
        }
        
        .settings-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.8);
        }
        
        .settings-button:hover .settings-tooltip {
            opacity: 1;
            visibility: visible;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-left: 0.75rem;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }
        
        .user-details h6 {
            margin: 0 0 0.25rem 0;
            font-weight: 600;
            color: #333;
        }
        
        .user-details p {
            margin: 0;
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            .settings-button-fixed {
                top: 70px;
                left: 10px;
            }
            
            .settings-dropdown {
                right: auto;
                left: 0;
                min-width: 250px;
            }
        }
    </style>

    @if($style === 'floating')
        <!-- زر الإعدادات العائم -->
        <div class="{{ $position === 'fixed' ? 'settings-button-fixed' : '' }}">
            <button type="button" 
                    class="settings-button {{ $buttonSize }}" 
                    onclick="toggleSettingsDropdown(this)"
                    title="الإعدادات">
                <i class="settings-icon fas fa-cog"></i>
                @if($showText && $position !== 'fixed')
                    <span class="ms-2">إعدادات</span>
                @endif
                <div class="settings-tooltip">الإعدادات</div>
            </button>
            
            <!-- قائمة الإعدادات -->
            <div class="settings-dropdown">
                @if($currentUser)
                    <!-- معلومات المستخدم -->
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <h6>{{ $currentUser->name ?? 'المستخدم' }}</h6>
                            <p>{{ $currentUser->email ?? '<EMAIL>' }}</p>
                        </div>
                    </div>
                @endif
                
                <!-- خيارات الإعدادات -->
                <a href="{{ route('profile.edit') ?? '#' }}" class="settings-option profile">
                    <div class="settings-option-icon">
                        <i class="fas fa-user-edit"></i>
                    </div>
                    <div class="settings-option-text">
                        <div class="settings-option-label">الملف الشخصي</div>
                        <p class="settings-option-description">تحرير المعلومات الشخصية</p>
                    </div>
                </a>

                <a href="{{ route('user.settings.index') ?? '#' }}" class="settings-option privacy">
                    <div class="settings-option-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="settings-option-text">
                        <div class="settings-option-label">الخصوصية والأمان</div>
                        <p class="settings-option-description">إدارة إعدادات الخصوصية</p>
                    </div>
                </a>

                <div class="settings-option notifications" onclick="toggleNotifications()">
                    <div class="settings-option-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="settings-option-text">
                        <div class="settings-option-label">الإشعارات</div>
                        <p class="settings-option-description">إدارة الإشعارات</p>
                    </div>
                </div>

                <div class="settings-option theme" onclick="toggleTheme()">
                    <div class="settings-option-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="settings-option-text">
                        <div class="settings-option-label">المظهر</div>
                        <p class="settings-option-description">تغيير المظهر والألوان</p>
                    </div>
                </div>

                @if($currentUser)
                    <div class="settings-option logout" onclick="confirmLogout()">
                        <div class="settings-option-icon">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <div class="settings-option-text">
                            <div class="settings-option-label">تسجيل الخروج</div>
                            <p class="settings-option-description">الخروج من الحساب</p>
                        </div>
                    </div>
                @else
                    <a href="{{ route('login') ?? '#' }}" class="settings-option profile">
                        <div class="settings-option-icon">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <div class="settings-option-text">
                            <div class="settings-option-label">تسجيل الدخول</div>
                            <p class="settings-option-description">الدخول إلى حسابك</p>
                        </div>
                    </a>
                @endif
            </div>
        </div>
    @elseif($style === 'button')
        <!-- زر الإعدادات العادي -->
        <button type="button" 
                class="settings-button-inline {{ $size === 'small' ? 'btn-sm' : ($size === 'large' ? 'btn-lg' : '') }}" 
                onclick="toggleSettingsDropdown(this)">
            <i class="settings-icon fas fa-cog"></i>
            @if($showText)
                <span>الإعدادات</span>
            @endif
        </button>
    @else
        <!-- أيقونة الإعدادات فقط -->
        <button type="button" 
                class="btn btn-link p-0 text-secondary" 
                onclick="toggleSettingsDropdown(this)"
                title="الإعدادات">
            <i class="settings-icon fas fa-cog {{ $size === 'small' ? 'fa-sm' : ($size === 'large' ? 'fa-lg' : '') }}"></i>
        </button>
    @endif
</div>

<script>
// إدارة قائمة الإعدادات
function toggleSettingsDropdown(button) {
    const dropdown = button.nextElementSibling || button.parentElement.querySelector('.settings-dropdown');
    if (!dropdown) return;
    
    const isVisible = dropdown.classList.contains('show');
    
    // إغلاق جميع القوائم المفتوحة
    document.querySelectorAll('.settings-dropdown.show').forEach(d => {
        d.classList.remove('show');
    });
    
    // فتح/إغلاق القائمة الحالية
    if (!isVisible) {
        dropdown.classList.add('show');
    }
}

// تبديل الإشعارات
function toggleNotifications() {
    const currentState = localStorage.getItem('notifications_enabled') !== 'false';
    const newState = !currentState;
    
    localStorage.setItem('notifications_enabled', newState);
    
    showSettingsMessage(
        newState ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات',
        newState ? 'success' : 'warning'
    );
}

// تبديل المظهر
function toggleTheme() {
    const currentTheme = localStorage.getItem('theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    
    localStorage.setItem('theme', newTheme);
    document.body.classList.toggle('dark-theme', newTheme === 'dark');
    
    showSettingsMessage(
        `تم تغيير المظهر إلى ${newTheme === 'dark' ? 'الداكن' : 'الفاتح'}`,
        'info'
    );
}

// تأكيد تسجيل الخروج
function confirmLogout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // إنشاء نموذج مخفي لتسجيل الخروج
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("logout") ?? "/logout" }}';
        
        // إضافة CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = csrfToken.getAttribute('content');
            form.appendChild(tokenInput);
        }
        
        document.body.appendChild(form);
        form.submit();
    }
}

// إظهار رسائل الإعدادات
function showSettingsMessage(message, type = 'info') {
    const colors = {
        'success': '#28a745',
        'info': '#007bff',
        'warning': '#ffc107',
        'error': '#dc3545'
    };
    
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 140px;
        left: 20px;
        background: ${colors[type]};
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 1002;
        opacity: 0;
        transform: translateX(-100%);
        transition: all 0.3s ease;
        max-width: 280px;
    `;
    
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'} me-2"></i>
        ${message}
    `;
    
    document.body.appendChild(notification);
    
    // إظهار الإشعار
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // إخفاء الإشعار
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(-100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}

// إغلاق القائمة عند النقر خارجها
document.addEventListener('click', function(event) {
    if (!event.target.closest('.settings-button-container')) {
        document.querySelectorAll('.settings-dropdown.show').forEach(dropdown => {
            dropdown.classList.remove('show');
        });
    }
});

// إغلاق القائمة عند الضغط على Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        document.querySelectorAll('.settings-dropdown.show').forEach(dropdown => {
            dropdown.classList.remove('show');
        });
    }
});

// تطبيق المظهر المحفوظ عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }
});
</script>
