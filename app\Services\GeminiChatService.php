<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class GeminiChatService
{
    private string $apiKey;
    private string $baseUrl;
    private array $systemPrompts;

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key', 'AIzaSyAicfjBq9fNTDQZ46CQg0TEglEoG7Lqr70');
        $baseUrl = config('services.gemini.base_url', 'https://generativelanguage.googleapis.com/v1beta');
        $model = config('services.gemini.model', 'gemini-1.5-flash');
        $this->baseUrl = $baseUrl . '/models/' . $model . ':generateContent';
        
        $this->systemPrompts = [
            'ar' => "أنت مساعد ذكي لموقع إعلانات متنوعة. مهمتك مساعدة المستخدمين في:\n" .
                   "- التنقل في الموقع\n" .
                   "- شرح كيفية نشر الإعلانات\n" .
                   "- البحث عن المنتجات والخدمات\n" .
                   "- حل المشاكل التقنية\n" .
                   "- تقديم معلومات عن الموقع وخدماته\n" .
                   "أجب بطريقة ودودة ومفيدة باللغة العربية. إذا لم تعرف الإجابة، اعتذر واطلب من المستخدم التواصل مع الدعم الفني.",
            
            'en' => "You are a smart assistant for a diverse classified ads website. Your job is to help users with:\n" .
                   "- Navigating the website\n" .
                   "- Explaining how to post ads\n" .
                   "- Searching for products and services\n" .
                   "- Solving technical issues\n" .
                   "- Providing information about the site and its services\n" .
                   "Answer in a friendly and helpful manner in English. If you don't know the answer, apologize and ask the user to contact technical support."
        ];
    }

    /**
     * Send message to Gemini AI and get response
     */
    public function sendMessage(string $message, string $language = 'ar', array $conversationHistory = []): array
    {
        try {
            $systemPrompt = $this->systemPrompts[$language] ?? $this->systemPrompts['ar'];
            
            // Build conversation context
            $context = $this->buildContext($systemPrompt, $conversationHistory, $message);
            
            $timeout = config('services.gemini.timeout', 30);
            $temperature = config('services.gemini.temperature', 0.7);
            $maxTokens = config('services.gemini.max_tokens', 1024);

            // Debug logging
            Log::info('Gemini API Request', [
                'url' => $this->baseUrl,
                'api_key_length' => strlen($this->apiKey),
                'message' => $message,
                'language' => $language
            ]);

            $response = Http::timeout($timeout)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($this->baseUrl . '?key=' . $this->apiKey, [
                    'contents' => [
                        [
                            'parts' => [
                                ['text' => $context]
                            ]
                        ]
                    ],
                    'generationConfig' => [
                        'temperature' => $temperature,
                        'topK' => 40,
                        'topP' => 0.95,
                        'maxOutputTokens' => $maxTokens,
                    ],
                    'safetySettings' => [
                        [
                            'category' => 'HARM_CATEGORY_HARASSMENT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ],
                        [
                            'category' => 'HARM_CATEGORY_HATE_SPEECH',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ],
                        [
                            'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ],
                        [
                            'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ]
                    ]
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                    return [
                        'success' => true,
                        'message' => trim($data['candidates'][0]['content']['parts'][0]['text']),
                        'usage' => $data['usageMetadata'] ?? null
                    ];
                }
            }

            Log::error('Gemini API Error', [
                'status' => $response->status(),
                'response' => $response->body(),
                'url' => $this->baseUrl,
                'message' => $message,
                'language' => $language
            ]);

            // تحليل نوع الخطأ
            $responseData = $response->json();
            $errorMessage = $language === 'ar'
                ? 'عذراً، حدث خطأ في الخدمة. يرجى المحاولة مرة أخرى.'
                : 'Sorry, there was a service error. Please try again.';

            if (isset($responseData['error']['message'])) {
                $apiError = $responseData['error']['message'];
                if (str_contains($apiError, 'API key')) {
                    $errorMessage = $language === 'ar'
                        ? 'خطأ في مفتاح API. يرجى التواصل مع الدعم الفني.'
                        : 'API key error. Please contact technical support.';
                } elseif (str_contains($apiError, 'quota') || str_contains($apiError, 'limit')) {
                    $errorMessage = $language === 'ar'
                        ? 'تم تجاوز الحد المسموح. يرجى المحاولة لاحقاً.'
                        : 'Rate limit exceeded. Please try again later.';
                }
            }

            return [
                'success' => false,
                'message' => $errorMessage,
                'error' => 'API_ERROR',
                'details' => $responseData['error'] ?? null
            ];

        } catch (Exception $e) {
            Log::error('Gemini Service Exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => $language === 'ar' 
                    ? 'عذراً، لا يمكنني الرد في الوقت الحالي. يرجى المحاولة لاحقاً.' 
                    : 'Sorry, I cannot respond right now. Please try again later.',
                'error' => 'EXCEPTION'
            ];
        }
    }

    /**
     * Build conversation context
     */
    private function buildContext(string $systemPrompt, array $history, string $currentMessage): string
    {
        $context = $systemPrompt . "\n\n";
        
        // Add recent conversation history (last 5 exchanges)
        $recentHistory = array_slice($history, -5);
        foreach ($recentHistory as $exchange) {
            $context .= "المستخدم: " . $exchange['user_message'] . "\n";
            $context .= "المساعد: " . $exchange['ai_response'] . "\n\n";
        }
        
        $context .= "المستخدم: " . $currentMessage . "\n";
        $context .= "المساعد: ";
        
        return $context;
    }

    /**
     * Get predefined quick responses
     */
    public function getQuickResponses(string $language = 'ar'): array
    {
        $responses = [
            'ar' => [
                'كيف أنشر إعلان؟',
                'كيف أبحث عن منتج؟',
                'ما هي رسوم النشر؟',
                'كيف أتواصل مع البائع؟',
                'كيف أحذف إعلاني؟'
            ],
            'en' => [
                'How do I post an ad?',
                'How do I search for a product?',
                'What are the posting fees?',
                'How do I contact the seller?',
                'How do I delete my ad?'
            ]
        ];

        return $responses[$language] ?? $responses['ar'];
    }

    /**
     * Test API connection
     */
    public function testConnection(): array
    {
        try {
            $testMessage = "Hello, this is a test message.";
            $response = $this->sendMessage($testMessage, 'en', []);

            return [
                'success' => $response['success'],
                'message' => $response['success'] ? 'API connection successful!' : $response['message'],
                'api_key_valid' => $response['success'],
                'model' => config('services.gemini.model'),
                'base_url' => config('services.gemini.base_url')
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'api_key_valid' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get available models (for debugging)
     */
    public function getAvailableModels(): array
    {
        try {
            $response = Http::timeout(30)
                ->get(config('services.gemini.base_url') . '/models?key=' . $this->apiKey);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'models' => $response->json()['models'] ?? []
                ];
            }

            return [
                'success' => false,
                'message' => 'Failed to fetch models',
                'status' => $response->status()
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
