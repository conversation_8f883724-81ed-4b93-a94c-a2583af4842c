<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class GeminiChatService
{
    private string $apiKey;
    private string $baseUrl;
    private array $systemPrompts;

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key', 'AIzaSyAicfjBq9fNTDQZ46CQg0TEglEoG7Lqr70');
        $this->baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
        
        $this->systemPrompts = [
            'ar' => "أنت مساعد ذكي لموقع إعلانات متنوعة. مهمتك مساعدة المستخدمين في:\n" .
                   "- التنقل في الموقع\n" .
                   "- شرح كيفية نشر الإعلانات\n" .
                   "- البحث عن المنتجات والخدمات\n" .
                   "- حل المشاكل التقنية\n" .
                   "- تقديم معلومات عن الموقع وخدماته\n" .
                   "أجب بطريقة ودودة ومفيدة باللغة العربية. إذا لم تعرف الإجابة، اعتذر واطلب من المستخدم التواصل مع الدعم الفني.",
            
            'en' => "You are a smart assistant for a diverse classified ads website. Your job is to help users with:\n" .
                   "- Navigating the website\n" .
                   "- Explaining how to post ads\n" .
                   "- Searching for products and services\n" .
                   "- Solving technical issues\n" .
                   "- Providing information about the site and its services\n" .
                   "Answer in a friendly and helpful manner in English. If you don't know the answer, apologize and ask the user to contact technical support."
        ];
    }

    /**
     * Send message to Gemini AI and get response
     */
    public function sendMessage(string $message, string $language = 'ar', array $conversationHistory = []): array
    {
        try {
            $systemPrompt = $this->systemPrompts[$language] ?? $this->systemPrompts['ar'];
            
            // Build conversation context
            $context = $this->buildContext($systemPrompt, $conversationHistory, $message);
            
            $response = Http::timeout(30)
                ->post($this->baseUrl . '?key=' . $this->apiKey, [
                    'contents' => [
                        [
                            'parts' => [
                                ['text' => $context]
                            ]
                        ]
                    ],
                    'generationConfig' => [
                        'temperature' => 0.7,
                        'topK' => 40,
                        'topP' => 0.95,
                        'maxOutputTokens' => 1024,
                    ],
                    'safetySettings' => [
                        [
                            'category' => 'HARM_CATEGORY_HARASSMENT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ],
                        [
                            'category' => 'HARM_CATEGORY_HATE_SPEECH',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ]
                    ]
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                    return [
                        'success' => true,
                        'message' => trim($data['candidates'][0]['content']['parts'][0]['text']),
                        'usage' => $data['usageMetadata'] ?? null
                    ];
                }
            }

            Log::error('Gemini API Error', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [
                'success' => false,
                'message' => $language === 'ar' 
                    ? 'عذراً، حدث خطأ في الخدمة. يرجى المحاولة مرة أخرى.' 
                    : 'Sorry, there was a service error. Please try again.',
                'error' => 'API_ERROR'
            ];

        } catch (Exception $e) {
            Log::error('Gemini Service Exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => $language === 'ar' 
                    ? 'عذراً، لا يمكنني الرد في الوقت الحالي. يرجى المحاولة لاحقاً.' 
                    : 'Sorry, I cannot respond right now. Please try again later.',
                'error' => 'EXCEPTION'
            ];
        }
    }

    /**
     * Build conversation context
     */
    private function buildContext(string $systemPrompt, array $history, string $currentMessage): string
    {
        $context = $systemPrompt . "\n\n";
        
        // Add recent conversation history (last 5 exchanges)
        $recentHistory = array_slice($history, -5);
        foreach ($recentHistory as $exchange) {
            $context .= "المستخدم: " . $exchange['user_message'] . "\n";
            $context .= "المساعد: " . $exchange['ai_response'] . "\n\n";
        }
        
        $context .= "المستخدم: " . $currentMessage . "\n";
        $context .= "المساعد: ";
        
        return $context;
    }

    /**
     * Get predefined quick responses
     */
    public function getQuickResponses(string $language = 'ar'): array
    {
        $responses = [
            'ar' => [
                'كيف أنشر إعلان؟',
                'كيف أبحث عن منتج؟',
                'ما هي رسوم النشر؟',
                'كيف أتواصل مع البائع؟',
                'كيف أحذف إعلاني؟'
            ],
            'en' => [
                'How do I post an ad?',
                'How do I search for a product?',
                'What are the posting fees?',
                'How do I contact the seller?',
                'How do I delete my ad?'
            ]
        ];

        return $responses[$language] ?? $responses['ar'];
    }
}
